
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RefreshCw, Loader2 } from "lucide-react";
import { EmptyFieldPlaceholder } from "../EmptyFieldPlaceholder";
import { toast } from "@/hooks/use-toast";
import { apiService } from "@/services/api";

interface BlogSeoSectionProps {
  metaTitle: string;
  metaDescription: string;
  onMetaTitleChange: (metaTitle: string) => void;
  onMetaDescriptionChange: (metaDescription: string) => void;
  tocString: string;
  isFieldMissing: (fieldName: string) => boolean;
  getMissingFieldStyle: (fieldName: string) => string;
}

export const BlogSeoSection = ({
  metaTitle,
  metaDescription,
  onMetaTitleChange,
  onMetaDescriptionChange,
  tocString,
  isFieldMissing,
  getMissingFieldStyle
}: BlogSeoSectionProps) => {
  const [isRegeneratingSeo, setIsRegeneratingSeo] = useState(false);

  const regenerateSeo = async () => {
    setIsRegeneratingSeo(true);
    try {
      const tocArray = tocString.split('\n').filter(item => item.trim() !== '');
      const response = await apiService.generateMeta(tocArray);

      if (response.success && response.data) {
        const newMetaTitle = response.data.meta_title || '';
        const newMetaDescription = response.data.meta_description || '';

        console.log('🔍 BlogSeoSection Generated:', { newMetaTitle, newMetaDescription });

        onMetaTitleChange(newMetaTitle);
        onMetaDescriptionChange(newMetaDescription);

        console.log('🔍 BlogSeoSection Parent Updated');

        toast({
          title: "SEO Content Regenerated",
          description: "Meta title and description have been regenerated successfully!",
        });
      } else {
        throw new Error(response.data?.error || response.message || "Failed to generate SEO content");
      }
    } catch (error) {
      console.error("SEO generation error:", error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to regenerate SEO content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRegeneratingSeo(false);
    }
  };

  return (
    <div className={`space-y-4 border rounded-lg p-4 bg-gray-50/50 ${(isFieldMissing('meta title') || isFieldMissing('meta description')) ? 'border-orange-300 bg-orange-50' : 'border-gray-200'}`}>
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-semibold text-gray-900">SEO Settings</h4>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={regenerateSeo}
          disabled={isRegeneratingSeo}
          className="flex items-center space-x-1"
        >
          {isRegeneratingSeo ? (
            <Loader2 className="h-3 w-3 animate-spin" />
          ) : (
            <RefreshCw className="h-3 w-3" />
          )}
          <span>Regenerate SEO</span>
        </Button>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="edit-meta-title" className="text-sm font-medium">
            Meta Title {isFieldMissing('meta title') && <span className="text-orange-500">*</span>}
          </Label>
          <Input
            id="edit-meta-title"
            value={metaTitle}
            onChange={e => onMetaTitleChange(e.target.value)}
            className={getMissingFieldStyle('meta title')}
          />
          {isFieldMissing('meta title') && !metaTitle && (
            <EmptyFieldPlaceholder
              fieldName="Meta Title"
              message="Meta title needs to be added"
              className="mt-1 py-2"
            />
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="edit-meta-description" className="text-sm font-medium">
            Meta Description {isFieldMissing('meta description') && <span className="text-orange-500">*</span>}
          </Label>
          <Textarea
            id="edit-meta-description"
            value={metaDescription}
            onChange={e => onMetaDescriptionChange(e.target.value)}
            rows={3}
            className={`resize-none ${getMissingFieldStyle('meta description')}`}
            placeholder="SEO meta description..."
          />
          {isFieldMissing('meta description') && !metaDescription && (
            <EmptyFieldPlaceholder
              fieldName="Meta Description"
              message="Meta description needs to be added"
              className="mt-1 py-2"
            />
          )}
        </div>
      </div>
    </div>
  );
};
