
import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Image, Upload, RefreshCw, Loader2 } from "lucide-react";
import { EmptyFieldPlaceholder } from "../EmptyFieldPlaceholder";
import { toast } from "@/hooks/use-toast";
import { apiService } from "@/services/api";

interface BlogImageSectionProps {
  imageUrl: string;
  onImageChange: (imageUrl: string) => void;
  tocString: string;
  isFieldMissing: (fieldName: string) => boolean;
  getMissingFieldStyle: (fieldName: string) => string;
}

export const BlogImageSection = ({
  imageUrl,
  onImageChange,
  tocString,
  isFieldMissing,
  getMissingFieldStyle
}: BlogImageSectionProps) => {
  const [isRegeneratingImage, setIsRegeneratingImage] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const regenerateImage = async () => {
    setIsRegeneratingImage(true);
    try {
      const tocArray = tocString.split('\n').filter(item => item.trim() !== '');
      const response = await apiService.generateHeroImage(tocArray);

      if (response.success && response.data) {
        onImageChange(response.data.hero_image_url || '');

        toast({
          title: "Image Regenerated",
          description: "New hero image has been generated successfully!",
        });
      } else {
        throw new Error(response.data?.error || response.message || "Failed to generate image");
      }
    } catch (error) {
      console.error("Image generation error:", error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to regenerate image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRegeneratingImage(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File",
        description: "Please upload an image file.",
        variant: "destructive",
      });
      return;
    }

    setIsUploadingImage(true);
    try {
      const uploadedImageUrl = URL.createObjectURL(file);
      onImageChange(uploadedImageUrl);

      toast({
        title: "Image Uploaded",
        description: "Image has been uploaded successfully!",
      });
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  return (
    <div className={`space-y-4 border rounded-lg p-4 bg-gray-50/50 ${isFieldMissing('hero image') ? 'border-orange-300 bg-orange-50' : 'border-gray-200'}`}>
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <Image className="h-5 w-5" />
          <span>Featured Image {isFieldMissing('hero image') && <span className="text-orange-500">*</span>}</span>
        </h4>
        <div className="flex space-x-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploadingImage}
            className="flex items-center space-x-1"
          >
            {isUploadingImage ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Upload className="h-3 w-3" />
            )}
            <span>Upload</span>
          </Button>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={regenerateImage}
            disabled={isRegeneratingImage}
            className="flex items-center space-x-1"
          >
            {isRegeneratingImage ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3" />
            )}
            <span>Generate</span>
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="edit-image" className="text-sm font-medium">Image URL</Label>
          <Input
            id="edit-image"
            value={imageUrl}
            onChange={e => onImageChange(e.target.value)}
            placeholder="https://example.com/image.jpg or upload a file"
            className={getMissingFieldStyle('hero image')}
          />
        </div>

        {imageUrl ? (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Preview</Label>
            <div className="relative rounded-lg overflow-hidden border">
              <img
                src={imageUrl}
                alt="Featured image preview"
                className="w-full h-32 object-cover"
                onError={(e) => {
                  // Remove fallback image - just hide the image if it fails to load
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
          </div>
        ) : isFieldMissing('hero image') && (
          <EmptyFieldPlaceholder
            fieldName="Hero Image"
            message="Image needs to be added or generated"
            className="mt-2 py-4"
          />
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        className="hidden"
      />
    </div>
  );
};
