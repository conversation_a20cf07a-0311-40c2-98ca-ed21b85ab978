import os
import re
import json
import time
import random
import string
import urllib.parse
import requests
import datetime
import markdown2
import boto3
import autogen
import ssl
import smtplib
import traceback
import base64
import uuid
import mimetypes
from bs4 import BeautifulSoup
from typing import Annotated
from botocore.exceptions import NoCredentialsError
from dotenv import load_dotenv
from email.message import EmailMessage
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from nltk.corpus import stopwords as nltk_stopwords
from autogen import AssistantAgent
from app.utils.config import llm_config, llm_config_claude
from google.oauth2 import service_account
from google.cloud import storage

# Initialize stopwords
try:
    stopwords = set(nltk_stopwords.words("english"))
except:
    stopwords = set()

load_dotenv()
image_no = 0
tags = []
categories = []
meta_title = ""
meta_desc = ""


def save_string_to_file(data, title):
    folder_name = "output/blogs/"
    if not os.path.exists(folder_name):
        # Create the folder
        os.makedirs(folder_name)
        print(f"Folder '{folder_name}' created successfully.")
    else:
        print(f"Folder '{folder_name}' already exists.")
    title = re.sub(r"\W+", "", title)
    # Open the file in append mode, creating it if it doesn't exist
    with open(f"{folder_name}/{title}.md", "a+") as file:
        file.write("\n" + data + "\n")


def save_toc_to_file(data, title):
    folder_name = "output/tocs"
    if not os.path.exists(folder_name):
        # Create the folder
        os.makedirs(folder_name)
        print(f"Folder '{folder_name}' created successfully.")
    else:
        print(f"Folder '{folder_name}' already exists.")
    title = re.sub(r"\W+", "", title)
    # Open the file in write mode, creating it if it doesn't exist
    with open(f"{folder_name}/{title}.json", "w+") as file:
        file.write(data)


# Global variable to store TOC data for stateless API operation
_toc_data_store = None


def toc_to_json(
    input_string: Annotated[str, "The string to be appended to 'template.md'"],
) -> str:
    """
    Converts table of contents string to JSON format.
    For API backend, stores data in memory instead of file system.
    """
    global _toc_data_store

    lines = input_string.split("\n")
    result = []
    for line in lines:
        if line.strip():  # Only add non-empty lines
            result.append(line.strip())

    # Store in memory for stateless API operation
    _toc_data_store = result

    # For backward compatibility, also save to file if in development mode
    if os.environ.get("SAVE_TOC_TO_FILE", "false").lower() == "true":
        # Ensure data directory exists
        if not os.path.exists("data"):
            os.makedirs("data")
        with open("data/template.json", "w") as json_file:
            json.dump(result, json_file, indent=4)

    return "Saved. Type TERMINATE"


def get_toc_data():
    """
    Get the stored TOC data from memory.
    Returns the data stored by toc_to_json function.
    """
    global _toc_data_store
    return _toc_data_store


def clear_toc_data():
    """
    Clear the stored TOC data from memory.
    Useful for cleaning up between API calls.
    """
    global _toc_data_store
    _toc_data_store = None


def read_from_json(json_file_path):
    # If path doesn't include directory, assume it's in the data directory
    if "/" not in json_file_path and "\\" not in json_file_path:
        json_file_path = f"data/{json_file_path}"

    try:
        with open(json_file_path, "r") as file:
            data = json.load(file)
        return data
    except FileNotFoundError:
        print(f"{json_file_path} not found.")
    except json.JSONDecodeError:
        print(f"Error decoding the JSON from {json_file_path}")


def md_file_to_string(file_path):
    with open(file_path, "r") as file:
        md_string = file.read()
    return md_string


def convert_json_to_rich_text(json_data):
    formatted_content = []
    for line in json_data:
        line = line.strip()
        if line:
            # Use a regular expression to match patterns like "1.", "1.1", "1.1.", "1.1.1", "1.1.1."
            match = re.match(r"^(\d+(\.\d+)*\.?)\s*(.*)", line)
            if match:
                chapter_section = match.group(1).rstrip(".")
                title = match.group(3)
                levels = chapter_section.split(".")

                # Determine the level of the heading based on the number of periods
                if len(levels) == 1:
                    # It's a chapter
                    formatted_content.append(f"<b>{chapter_section}. {title}</b>")
                elif len(levels) == 2:
                    # It's a section
                    formatted_content.append(
                        f"&nbsp;&nbsp;&nbsp;&nbsp;{chapter_section}. {title}"
                    )
                elif len(levels) >= 3:
                    # It's a subsection
                    formatted_content.append(
                        f"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{chapter_section}. {title}"
                    )
            else:
                # Regular content line, add it as is
                formatted_content.append(line)

        # Add a line break after each paragraph unless the last element is a bold tag
        if not formatted_content[-1].startswith("<b>"):
            formatted_content.append("<br>")

    # Join the formatted content into a single string
    formatted_content_str = "".join(formatted_content)
    return formatted_content_str


def seo_optimizer(
    title: Annotated[str, "Meta title to be saved"],
    desc: Annotated[str, "Meta description to be saved"],
) -> str:
    global meta_title, meta_desc
    meta_title = title
    meta_desc = desc
    return "Saved"


def get_tags():
    api = os.getenv("WEBFLOW_API_KEY")
    collection_id = os.getenv("WEBFLOW_TAGS_COLLECTION_ID")
    url = f"https://api.webflow.com/v2/collections/{collection_id}/items"

    headers = {"accept": "application/json", "authorization": f"Bearer {api}"}

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        tags_data = response.json()

        # Extract tags information and format it
        tags = []
        for item in tags_data.get("items", []):
            field_data = item.get("fieldData", {})
            tag_name = field_data.get("name", "N/A")
            tag_id = item.get("id", "N/A")
            tags.append({"name": tag_name, "id": tag_id})

        # Save the table to a text file
    return tags


def get_categories():
    api = os.getenv("WEBFLOW_API_KEY")
    collection_id = os.getenv("WEBFLOW_CATEGORY_COLLECTION_ID")
    url = f"https://api.webflow.com/v2/collections/{collection_id}/items"

    headers = {"accept": "application/json", "authorization": f"Bearer {api}"}

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        categories_data = response.json()

        # Extract tags information and format it
        categories = []
        for item in categories_data.get("items", []):
            field_data = item.get("fieldData", {})
            category_name = field_data.get("name", "N/A")
            category_id = item.get("id", "N/A")
            categories.append({"name": category_name, "id": category_id})

        # Save the table to a text file

    return categories


def slug_generator(summary):
    slug_agent = autogen.AssistantAgent(
        name="Replying_Agent",
        system_message=f""" Your task is to read {summary} and create a url slug for the same.It should always be in this format " (^$)|^[_a-zA-Z0-9][-_a-zA-Z0-9]*$\\\\\\"\\"] "..Just give the slug url as output""",
        max_consecutive_auto_reply=1,
        llm_config=llm_config,
        # is_termination_msg=None,
        human_input_mode="NEVER",  # Never ask for human input
    )
    # Check if greeting is a string, convert to a dictionary with role as "user"
    if isinstance(summary, str):
        messages = [{"content": summary, "role": "user"}]
    else:
        messages = summary  # Assume greeting is already a list of dictionaries
    slug = slug_agent.generate_reply(messages=messages)
    return slug


def upload_file_to_s3(file_name):
    object_name = file_name
    access_key = os.getenv("AWS_ACCESS_KEY")
    secret_key = os.getenv("AWS_SECRET_KEY")
    bucket_name = os.getenv("AWS_BUCKET_NAME")
    region = os.getenv("AWS_REGION")

    # Create an S3 client
    if region is not None:
        s3_client = boto3.client(
            "s3",
            region_name=region,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
        )
    else:
        s3_client = boto3.client("s3")

    # Determine content type based on file extension

    if file_name.lower().endswith((".jpg", ".jpeg")):
        content_type = "image/jpeg"
    elif file_name.lower().endswith(".png"):
        content_type = "image/png"
    elif file_name.lower().endswith(".json"):
        content_type = "application/json"
    elif file_name.lower().endswith(".txt"):
        content_type = "text/plain"
    elif file_name.lower().endswith(".svg"):
        content_type = "image/svg+xml"
    else:
        print("Unsupported file type.")
        return False

    try:
        # Prepare extra arguments for upload
        extra_args = {"ContentType": content_type}
        # Upload the file with specified content type
        s3_client.upload_file(file_name, bucket_name, object_name, ExtraArgs=extra_args)
        print(
            f"File '{file_name}' uploaded successfully to bucket '{bucket_name}' with key '{object_name}'."
        )

        var = f"https://{bucket_name}.s3.amazonaws.com/{object_name}"
        return var
    except FileNotFoundError:
        print(f"The file '{file_name}' was not found.")
        return False
    except NoCredentialsError:
        print("AWS credentials not available.")
        return False
    except Exception as e:
        print(f"An error occurred: {e}")
        return False


def download_file_from_s3(local_file_path, key):
    access_key = os.getenv("AWS_ACCESS_KEY")
    secret_key = os.getenv("AWS_SECRET_KEY")
    bucket_name = os.getenv("AWS_BUCKET_NAME")
    region = os.getenv("AWS_REGION")

    # Create an S3 client
    if region:
        s3_client = boto3.client(
            "s3",
            region_name=region,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
        )
    else:
        s3_client = boto3.client("s3")

    try:
        # Download the file
        s3_client.download_file(bucket_name, key, local_file_path)
        print(f"File '{key}' downloaded successfully to '{local_file_path}'.")
    except FileNotFoundError:
        print(f"The file '{key}' was not found in bucket '{bucket_name}'.")
    except NoCredentialsError:
        print("AWS credentials not available.")
    except Exception as e:
        print(f"An error occurred: {e}")


def get_topics():
    api = os.getenv("WEBFLOW_API_KEY")
    collection_id = os.getenv("WEBFLOW_COLLECTION_ID")
    base_url = f"https://api.webflow.com/v2/collections/{collection_id}/items"

    headers = {"accept": "application/json", "authorization": f"Bearer {api}"}

    offset = 0
    limit = 100
    all_items = []

    while True:
        url = f"{base_url}?offset={offset}&limit={limit}"
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            items = data.get("items", [])
            all_items.extend(items)

            # Debugging: Print current offset and number of items fetched in this batch
            print(f"Offset: {offset}, Items fetched: {len(items)}")

            if len(items) < limit:
                break

            offset += limit
        else:
            print(f"Failed to fetch topics. {response.text}")
            break

    categories = []
    for item in all_items:
        field_data = item.get("fieldData", {})
        category_name = field_data.get("name", "N/A")
        categories.append(category_name)

    # Ensure data directory exists
    if not os.path.exists("data"):
        os.makedirs("data")

    # Save the categories to a text file
    with open("data/used_topics.txt", "w") as file:
        json.dump(categories, file, indent=4)

    # Print the number of entries in the file
    print(f"Number of entries in the file: {len(categories)}")


def image_generator1(
    prompt: Annotated[str, "prompt to be passed for the diagram generation"],
) -> str:
    url = "https://app.eraser.io/api/render/prompt"
    api_key = os.getenv("ERASER_API_KEY")
    attempt = 0
    max_attempts = 1  # Maximum number of retry attempts
    payload = {"text": prompt, "returnFile": False, "scale": "1"}
    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "authorization": f"Bearer {api_key}",
    }

    while attempt < max_attempts:
        try:
            response = requests.post(url, json=payload, headers=headers)
            # Check if response status code is 502
            if response.status_code == 502:
                soup = BeautifulSoup(response.text, "html.parser")
                # Extract the error message
                error_message = soup.title.text.strip()
                if "502" in error_message:
                    raise requests.HTTPError("Error 502: Server Error")
            # Check if response object is None or response status is not 200
            if response is None or response.status_code != 200:
                raise requests.HTTPError(f"Unexpected response: {response.text}")
            print(response.text)  # For debugging purposes
            data = json.loads(response.text)
            # Extract the image URL
            image_url = data["imageUrl"]
            return image_url
        except Exception as e:
            error_message = str(e)
            if "Error 502: Server Error" in error_message:
                print(f"Attempt {attempt + 1}: {error_message}")
                print(f"Retrying in 35 seconds...")
                time.sleep(35)
                attempt += 1
            else:
                return (
                    f"An error occurred: {error_message}.Please save the blog instead"
                )
    return "Maximum number of attempts reached. Please save the blog instead."


def get_random_image_path():
    # List all files in the directory
    folder_path = "app/core/content/dependencies/image_guidance"
    if not os.path.exists(folder_path):
        print(f"Directory does not exist: {folder_path}")
        return None

    files = os.listdir(folder_path)

    # Filter out non-image files
    image_files = [
        f
        for f in files
        if f.lower().endswith((".png", ".jpg", ".jpeg", ".gif", ".bmp"))
    ]

    if not image_files:
        print(f"No image files found in directory: {folder_path}")
        return None

    # Choose a random image file from the list
    random_image = random.choice(image_files)

    # Construct the full path to the image
    image_file_path = os.path.join(folder_path, random_image)

    return image_file_path


def sanitize_text(text):
    safe_characters = re.compile(r"[^a-zA-Z0-9\-._~]")

    # Replace spaces with hyphens
    url_safe_string = text.replace(" ", "-")

    # Remove any characters that are not alphanumeric or safe special characters
    url_safe_string = safe_characters.sub("", url_safe_string)

    # Limit filename length to prevent filesystem issues
    # Windows has a 260 character path limit, so we'll limit the filename to 100 characters
    max_filename_length = 100
    if len(url_safe_string) > max_filename_length:
        # Truncate and add a hash to ensure uniqueness
        import hashlib

        hash_suffix = hashlib.md5(text.encode()).hexdigest()[:8]
        url_safe_string = url_safe_string[: max_filename_length - 9] + "_" + hash_suffix

    # Encode the string to make sure it's URL safe
    url_safe_string = urllib.parse.quote(url_safe_string, safe="")

    return url_safe_string


def convert_markdown_to_rtf(md_input):
    def escape_html(text):
        return (
            text.replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace('"', "&quot;")
            .replace("'", "&#039;")
        )

    # Convert headers
    md_input = re.sub(r"^#\s+(.+)$", r"<h1>\1</h1>", md_input, flags=re.MULTILINE)
    md_input = re.sub(r"^##\s+(.+)$", r"<h2>\1</h2>", md_input, flags=re.MULTILINE)
    md_input = re.sub(r"^###\s+(.+)$", r"<h3>\1</h3>", md_input, flags=re.MULTILINE)

    # Split the input into paragraphs and code blocks
    parts = re.split(r"(```[\s\S]+?```)", md_input)

    rich_text = []
    for part in parts:
        if part.startswith("```"):
            # Process code blocks
            language_match = re.match(r"```(\w+)", part)
            language = language_match.group(1) if language_match else "plaintext"
            code_content = re.sub(
                r"^```\w*\n|```$", "", part, flags=re.MULTILINE
            ).strip()

            # Convert HTML comments back to Python comments in code blocks
            code_content = re.sub(r"<h1>(.*?)</h1>", r"# \1", code_content)
            code_content = re.sub(r"<h2>(.*?)</h2>", r"# \1", code_content)
            code_content = re.sub(r"<h3>(.*?)</h3>", r"# \1", code_content)

            # Preserve newlines in code blocks
            code_content = code_content.replace("\n", "-a1b2c3-")

            # Add language as the first line
            language_line = f'language="language-{language.lower()}"'
            code_content = f"{language_line}{code_content}"

            # Enclose the code block properly
            rich_text.append(f"<code>{escape_html(code_content)}</code>")
        else:
            # Use markdown2 to convert non-code-block parts
            html = markdown2.markdown(part)
            rich_text.append(html)

    # Join all parts
    html_content = "\n".join(rich_text)

    # Remove any remaining newlines
    html_content = html_content.replace("\n", " ")

    # Create a JSON object and stringify it
    html_string = json.dumps({"text": html_content})

    # Parse the stringified object to get the string value
    return json.loads(html_string)["text"]


def save_string_to_file(data, title):
    folder_name = "blogs"
    if not os.path.exists(folder_name):
        # Create the folder
        os.makedirs(folder_name)
        print(f"Folder '{folder_name}' created successfully.")
    else:
        print(f"Folder '{folder_name}' already exists.")
    title = re.sub(r"\W+", "", title)
    # Open the file in append mode, creating it if it doesn't exist
    with open(f"output/blogs/{title}.md", "a+") as file:
        file.write("\n" + data + "\n")


def save_toc_to_file(data, title):
    folder_name = "tocs"
    if not os.path.exists(folder_name):
        # Create the folder
        os.makedirs(folder_name)
        print(f"Folder '{folder_name}' created successfully.")
    else:
        print(f"Folder '{folder_name}' already exists.")
    title = re.sub(r"\W+", "", title)
    # Open the file in write mode, creating it if it doesn't exist
    with open(f"tocs/{title}.json", "w+") as file:
        file.write(data)


def generate_claude_sonnet(prompt):
    claude_agent = AssistantAgent(
        name="ChatBotGenerator",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=4,
        system_message=prompt,
        llm_config=llm_config_claude,
        # is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE")
    )
    if isinstance(prompt, str):
        messages = [{"content": prompt, "role": "user"}]
    else:
        messages = prompt  # Assume greeting is already a list of dictionaries

    claude_text = claude_agent.generate_reply(messages=messages)
    content_string = claude_text["content"]
    return content_string


def preprocess_text(text):
    text = text.lower()  # Convert text to lowercase
    text = text.translate(
        str.maketrans("", "", string.punctuation)
    )  # Remove punctuation
    tokens = text.split()  # Tokenize text
    tokens = [word for word in tokens if word not in stopwords]  # Remove stopwords
    return " ".join(tokens)  # Join tokens back into a single string


def get_sorted_links(blog_content, data):
    """
    This function preprocesses the blog content and link summaries, computes cosine similarity, and returns a list of URLs
    sorted by relevance.

    Parameters:
    - blog_content: The content of the blog (string)
    - data: A list of dictionaries where each dictionary contains 'url', 'title', and 'summary'

    Returns:
    - sorted_links: A list of URLs sorted by relevance in descending order
    """
    # Ensure blog content is not empty
    if not blog_content.strip():
        raise ValueError("Blog content is empty.")

    # Ensure data is not empty
    if not data:
        raise ValueError("Data is empty.")

    # Preprocess blog content
    blog_content_processed = preprocess_text(blog_content)

    # Preprocess summaries and keep track of the valid entries
    valid_entries = []
    processed_summaries = []
    for entry in data:
        processed_summary = preprocess_text(entry["summary"])
        if processed_summary:
            processed_summaries.append(processed_summary)
            valid_entries.append(entry)  # Only append valid entries

    # Combine blog content and processed summaries into a single list for TF-IDF
    texts = [blog_content_processed] + processed_summaries

    # Check if there are any valid texts for TF-IDF
    if len(texts) <= 1:
        raise ValueError("Not enough valid text data for similarity computation.")

    # Apply TF-IDF Vectorization
    tfidf_vectorizer = TfidfVectorizer()
    tfidf_matrix = tfidf_vectorizer.fit_transform(texts)

    # Compute cosine similarity between the blog content (first row) and each summary
    cosine_similarities = cosine_similarity(
        tfidf_matrix[0:1], tfidf_matrix[1:]
    ).flatten()

    # Attach similarity scores to each valid entry
    for i, entry in enumerate(valid_entries):
        entry["similarity"] = cosine_similarities[i]

    # Sort the valid entries based on the similarity scores in descending order
    sorted_data = sorted(valid_entries, key=lambda x: x["similarity"], reverse=True)

    # Extract and return the list of URLs sorted by relevance
    sorted_links = [entry["url"] for entry in sorted_data]

    return sorted_links


def send_email(html_content, receiver_email, subject, excel_file_path=None):
    """
    Sends an email with HTML content and an optional Excel file attachment.

    :param html_content: The HTML content for the email body.
    :param receiver_email: The recipient's email address.
    :param subject: The subject of the email.
    :param excel_file_path: Path to the Excel file to be attached (optional).
    """
    try:
        # Set up the SMTP server and credentials from environment variables
        sender_email = os.getenv("sender_email")
        sender_password = os.getenv("sender_password")
        smtp_server = os.getenv("smtp_server")
        email_port = os.getenv("email_port", 587)  # Default to port 587 if not set

        # Ensure all required environment variables are present
        if not all([sender_email, sender_password, smtp_server, email_port]):
            raise ValueError(
                "Missing required environment variables for email sending."
            )

        # Compose the email message
        msg = EmailMessage()
        msg["From"] = sender_email
        msg["To"] = receiver_email
        msg["Subject"] = subject

        # Add the HTML content to the email
        msg.add_alternative(html_content, subtype="html")

        # Attach the Excel file if provided
        if excel_file_path:
            if not os.path.exists(excel_file_path):
                raise FileNotFoundError(f"Attachment file not found: {excel_file_path}")
            with open(excel_file_path, "rb") as file:
                file_data = file.read()
                file_name = os.path.basename(excel_file_path)
                msg.add_attachment(
                    file_data,
                    maintype="application",
                    subtype="vnd.ms-excel",
                    filename=file_name,
                )
            print(f"Attached Excel file: {file_name}")

        # Create a secure SSL context
        context = ssl.create_default_context()

        # Connect to the SMTP server and send the email
        with smtplib.SMTP(smtp_server, email_port) as server:
            server.starttls(context=context)  # Secure the connection
            server.login(sender_email, sender_password)  # Login to the SMTP server
            server.send_message(msg)  # Send the email

        print("Email sent successfully")

    except FileNotFoundError as e:
        print(f"Attachment error: {e}")
    except smtplib.SMTPRecipientsRefused as e:
        print(f"Recipient refused: {e.recipients}")
    except smtplib.SMTPAuthenticationError:
        print("SMTP Authentication failed. Check your username and password.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        print(f"Traceback: {traceback.format_exc()}")
