"""
Unit tests for OpenAI hero image generation functionality.
"""

import unittest
import os
import base64
from unittest.mock import Mock, patch, MagicMock, mock_open
from io import BytesIO
from PIL import Image
import sys

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.core.publishing.hero_images_openai import (
    OpenAIImageGenerator,
    get_openai_image_generator,
    openai_text_to_image,
    openai_image_to_image,
    generate_hero_image_openai,
)


class TestOpenAIImageGenerator(unittest.TestCase):
    """Test cases for OpenAIImageGenerator class."""

    def setUp(self):
        """Set up test environment."""
        # Mock environment variables
        self.env_patcher = patch.dict(
            os.environ,
            {
                "OPENAI_API_KEY": "test_openai_key",
                "USE_OPENAI_IMAGES": "true",
                "OPENAI_IMAGE_DEFAULT_SIZE": "1024x1024",
                "OPENAI_IMAGE_DEFAULT_QUALITY": "high",
                "OPENAI_IMAGE_DEFAULT_FORMAT": "png",
            },
        )
        self.env_patcher.start()

        # Create a test image in bytes format
        self.test_image = Image.new("RGB", (100, 100), color="red")
        self.test_image_bytes = BytesIO()
        self.test_image.save(self.test_image_bytes, format="PNG")
        self.test_image_bytes = self.test_image_bytes.getvalue()

        # Mock base64 encoded image response
        self.mock_b64_image = base64.b64encode(self.test_image_bytes).decode()

    def tearDown(self):
        """Clean up test environment."""
        self.env_patcher.stop()

    @patch("app.core.publishing.hero_images_openai.OpenAI")
    def test_openai_image_generator_init(self, mock_openai_class):
        """Test OpenAIImageGenerator initialization."""
        generator = OpenAIImageGenerator()
        
        self.assertEqual(generator.api_key, "test_openai_key")
        self.assertEqual(generator.default_size, "1024x1024")
        self.assertEqual(generator.default_quality, "high")
        self.assertEqual(generator.default_output_format, "png")
        mock_openai_class.assert_called_once_with(api_key="test_openai_key")

    def test_openai_image_generator_init_no_api_key(self):
        """Test OpenAIImageGenerator initialization without API key."""
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError) as context:
                OpenAIImageGenerator()
            self.assertIn("OPENAI_API_KEY", str(context.exception))

    @patch("app.core.publishing.hero_images_openai.OpenAI")
    def test_text_to_image_success(self, mock_openai_class):
        """Test successful text-to-image generation."""
        # Mock OpenAI client and response
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.data = [Mock(b64_json=self.mock_b64_image)]
        mock_client.images.generate.return_value = mock_response

        generator = OpenAIImageGenerator()
        result = generator.text_to_image("test prompt")

        self.assertEqual(result, self.test_image_bytes)
        mock_client.images.generate.assert_called_once_with(
            model="gpt-image-1",
            prompt="test prompt",
            n=1,
            size="1024x1024",
            quality="high",
            background="auto",
            output_format="png",
            response_format="b64_json"
        )

    @patch("app.core.publishing.hero_images_openai.OpenAI")
    def test_text_to_image_with_custom_params(self, mock_openai_class):
        """Test text-to-image generation with custom parameters."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.data = [Mock(b64_json=self.mock_b64_image)]
        mock_client.images.generate.return_value = mock_response

        generator = OpenAIImageGenerator()
        result = generator.text_to_image(
            "test prompt",
            size="1536x1024",
            quality="medium",
            background="transparent",
            output_format="jpeg",
            n=2
        )

        mock_client.images.generate.assert_called_once_with(
            model="gpt-image-1",
            prompt="test prompt",
            n=2,
            size="1536x1024",
            quality="medium",
            background="transparent",
            output_format="jpeg",
            response_format="b64_json"
        )

    @patch("app.core.publishing.hero_images_openai.OpenAI")
    @patch("time.sleep")
    def test_text_to_image_retry_on_failure(self, mock_sleep, mock_openai_class):
        """Test retry mechanism on API failure."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # First two calls fail, third succeeds
        mock_client.images.generate.side_effect = [
            Exception("API Error 1"),
            Exception("API Error 2"),
            Mock(data=[Mock(b64_json=self.mock_b64_image)])
        ]

        generator = OpenAIImageGenerator()
        result = generator.text_to_image("test prompt")

        self.assertEqual(result, self.test_image_bytes)
        self.assertEqual(mock_client.images.generate.call_count, 3)
        self.assertEqual(mock_sleep.call_count, 2)

    @patch("app.core.publishing.hero_images_openai.OpenAI")
    @patch("time.sleep")
    def test_text_to_image_max_retries_exceeded(self, mock_sleep, mock_openai_class):
        """Test behavior when max retries are exceeded."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # All calls fail
        mock_client.images.generate.side_effect = Exception("Persistent API Error")

        generator = OpenAIImageGenerator()
        
        with self.assertRaises(Exception) as context:
            generator.text_to_image("test prompt")
        
        self.assertIn("Failed to generate image after 3 attempts", str(context.exception))
        self.assertEqual(mock_client.images.generate.call_count, 3)

    @patch("app.core.publishing.hero_images_openai.OpenAI")
    @patch("builtins.open", new_callable=mock_open, read_data=b"fake_image_data")
    @patch("os.path.exists")
    def test_image_to_image_success(self, mock_exists, mock_file_open, mock_openai_class):
        """Test successful image-to-image editing."""
        mock_exists.return_value = True
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.data = [Mock(b64_json=self.mock_b64_image)]
        mock_client.images.edit.return_value = mock_response

        generator = OpenAIImageGenerator()
        result = generator.image_to_image("edit prompt", ["test_image.jpg"])

        self.assertEqual(result, self.test_image_bytes)
        mock_client.images.edit.assert_called_once()

    @patch("app.core.publishing.hero_images_openai.OpenAI")
    @patch("requests.get")
    def test_image_to_image_with_url(self, mock_requests_get, mock_openai_class):
        """Test image-to-image editing with URL input."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.data = [Mock(b64_json=self.mock_b64_image)]
        mock_client.images.edit.return_value = mock_response

        # Mock HTTP response for image URL
        mock_http_response = Mock()
        mock_http_response.content = self.test_image_bytes
        mock_requests_get.return_value = mock_http_response

        generator = OpenAIImageGenerator()
        result = generator.image_to_image("edit prompt", ["https://example.com/image.jpg"])

        self.assertEqual(result, self.test_image_bytes)
        mock_requests_get.assert_called_once_with("https://example.com/image.jpg")

    def test_get_openai_image_generator(self):
        """Test get_openai_image_generator function."""
        with patch("app.core.publishing.hero_images_openai.OpenAIImageGenerator") as mock_class:
            mock_instance = Mock()
            mock_class.return_value = mock_instance
            
            result = get_openai_image_generator()
            
            self.assertEqual(result, mock_instance)
            mock_class.assert_called_once()

    @patch("app.core.publishing.hero_images_openai.get_openai_image_generator")
    def test_openai_text_to_image_function(self, mock_get_generator):
        """Test openai_text_to_image convenience function."""
        mock_generator = Mock()
        mock_generator.text_to_image.return_value = self.test_image_bytes
        mock_get_generator.return_value = mock_generator

        result = openai_text_to_image("test prompt", size="1024x1024")

        self.assertEqual(result, self.test_image_bytes)
        mock_generator.text_to_image.assert_called_once_with("test prompt", size="1024x1024")

    @patch("app.core.publishing.hero_images_openai.get_openai_image_generator")
    def test_openai_image_to_image_function(self, mock_get_generator):
        """Test openai_image_to_image convenience function."""
        mock_generator = Mock()
        mock_generator.image_to_image.return_value = self.test_image_bytes
        mock_get_generator.return_value = mock_generator

        result = openai_image_to_image("edit prompt", ["image.jpg"], size="1024x1024")

        self.assertEqual(result, self.test_image_bytes)
        mock_generator.image_to_image.assert_called_once_with(
            "edit prompt", ["image.jpg"], size="1024x1024"
        )


class TestGenerateHeroImageOpenAI(unittest.TestCase):
    """Test cases for generate_hero_image_openai function."""

    def setUp(self):
        """Set up test environment."""
        self.env_patcher = patch.dict(
            os.environ,
            {
                "OPENAI_API_KEY": "test_openai_key",
                "USE_OPENAI_IMAGES": "true",
            },
        )
        self.env_patcher.start()

        # Create test image
        self.test_image = Image.new("RGB", (100, 100), color="blue")
        self.test_image_bytes = BytesIO()
        self.test_image.save(self.test_image_bytes, format="PNG")
        self.test_image_bytes = self.test_image_bytes.getvalue()

    def tearDown(self):
        """Clean up test environment."""
        self.env_patcher.stop()

    @patch("app.core.publishing.hero_images_openai.add_text_to_image")
    @patch("app.core.publishing.hero_images_openai.openai_text_to_image")
    @patch("app.core.publishing.hero_images_openai.generate_prompt")
    def test_generate_hero_image_text_to_image_mode(
        self, mock_generate_prompt, mock_text_to_image, mock_add_text
    ):
        """Test hero image generation in text-to-image mode."""
        mock_generate_prompt.return_value = "Generated prompt"
        mock_text_to_image.return_value = self.test_image_bytes
        mock_add_text.return_value = "https://s3.amazonaws.com/test-image.jpg"

        result = generate_hero_image_openai("Test content", use_reference_image=False)

        self.assertEqual(result, "https://s3.amazonaws.com/test-image.jpg")
        mock_generate_prompt.assert_called_once_with("Test content")
        mock_text_to_image.assert_called_once_with("Generated prompt", size="1024x544", quality="high")

    @patch("app.core.publishing.hero_images_openai.add_text_to_image")
    @patch("app.core.publishing.hero_images_openai.openai_image_to_image")
    @patch("app.core.publishing.hero_images_openai.get_random_image_path")
    @patch("app.core.publishing.hero_images_openai.generate_prompt")
    def test_generate_hero_image_image_to_image_mode(
        self, mock_generate_prompt, mock_get_random_path, mock_image_to_image, mock_add_text
    ):
        """Test hero image generation in image-to-image mode."""
        mock_generate_prompt.return_value = "Generated prompt"
        mock_get_random_path.return_value = "/path/to/reference.jpg"
        mock_image_to_image.return_value = self.test_image_bytes
        mock_add_text.return_value = "https://s3.amazonaws.com/test-image.jpg"

        result = generate_hero_image_openai("Test content", use_reference_image=True)

        self.assertEqual(result, "https://s3.amazonaws.com/test-image.jpg")
        mock_generate_prompt.assert_called_once_with("Test content")
        mock_image_to_image.assert_called_once_with(
            "Generated prompt", "/path/to/reference.jpg", size="1024x544"
        )

    @patch("app.core.publishing.hero_images_openai.generate_prompt")
    def test_generate_hero_image_with_list_input(self, mock_generate_prompt):
        """Test hero image generation with list input."""
        mock_generate_prompt.return_value = "Generated prompt"
        
        with patch("app.core.publishing.hero_images_openai.openai_text_to_image") as mock_text_to_image:
            with patch("app.core.publishing.hero_images_openai.add_text_to_image") as mock_add_text:
                mock_text_to_image.return_value = self.test_image_bytes
                mock_add_text.return_value = "https://s3.amazonaws.com/test-image.jpg"

                result = generate_hero_image_openai(["Item 1", "Item 2"], use_reference_image=False)

                mock_generate_prompt.assert_called_once_with("Item 1,Item 2")

    @patch("app.core.publishing.hero_images_openai.generate_prompt")
    def test_generate_hero_image_upload_failure(self, mock_generate_prompt):
        """Test hero image generation when S3 upload fails."""
        mock_generate_prompt.return_value = "Generated prompt"
        
        with patch("app.core.publishing.hero_images_openai.openai_text_to_image") as mock_text_to_image:
            with patch("app.core.publishing.hero_images_openai.add_text_to_image") as mock_add_text:
                mock_text_to_image.return_value = self.test_image_bytes
                mock_add_text.return_value = False  # Upload failure

                with self.assertRaises(Exception) as context:
                    generate_hero_image_openai("Test content")

                self.assertIn("Failed to upload image to S3", str(context.exception))


if __name__ == "__main__":
    unittest.main()
