"""
Content utilities API endpoints.
"""

from flask import request, jsonify
from . import api_bp
from app.core.content.toc import (
    pillar_toc_generator,
    toc_generator,
    toc_generator_short,
    toc_generator_technical,
)
from app.core.publishing.hero_images import hero_image_generation
from app.core.publishing.hero_images_openai import generate_hero_image_openai
from app.utils.config import should_use_openai_images, get_image_generation_mode
from app.utils.helpers import generate_claude_sonnet
from app.core.content.charts import table_chart_generation_new
from app.core.analysis.links import fetch_entries_with_specific_link
from app.core.publishing.wordpress import post_to_wordpress, retry_on_529_error
from app.core.publishing.metadata import meta_description_and_title, get_summary
from app.utils.helpers import slug_generator
from app.utils.auth import require_api_key


@api_bp.route("/content/toc", methods=["POST"])
@require_api_key
def generate_table_of_contents():
    """
    Generate table of contents for a given topic using the same logic as exec.py.

    Request JSON:
    {
        "topic": "Topic for TOC generation",
        "blog_type": "informative|technical (default: informative)",
        "blog_length": "short|medium|long (default: medium)",
        "prompt": "Optional custom prompt (for pillar TOC)"
    }

    Response JSON:
    {
        "table_of_contents": ["Generated TOC as list"],
        "toc_type": "standard|short|technical|pillar"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        topic = data.get("topic")
        blog_type = data.get("blog_type", "informative").lower()
        blog_length = data.get("blog_length", "medium").lower()
        prompt = data.get("prompt", "")

        if not topic:
            return jsonify({"error": "Missing required field: topic"}), 400

        # Validate enum fields
        if blog_type not in ["informative", "technical"]:
            return (
                jsonify({"error": "blog_type must be 'informative' or 'technical'"}),
                400,
            )

        if blog_length not in ["short", "medium", "long"]:
            return (
                jsonify({"error": "blog_length must be 'short', 'medium', or 'long'"}),
                400,
            )

        # Generate TOC using the same logic as exec.py
        # if prompt:
        #     # If custom prompt is provided, use pillar TOC generator
        #     print(f"Generating pillar TOC for topic: {topic}")
        #     table_of_contents = pillar_toc_generator(topic, prompt)
        #     toc_type = "pillar"
        if blog_type == "technical":
            print(f"Generating technical TOC for topic: {topic}")
            table_of_contents = toc_generator_technical(topic)
            toc_type = "technical"
        elif blog_length == "short":
            print(f"Generating short informative TOC for topic: {topic}")
            table_of_contents = toc_generator_short(topic)
            toc_type = "short"
        else:
            print(f"Generating standard informative TOC for topic: {topic}")
            table_of_contents = toc_generator(topic)
            toc_type = "standard"

        if not table_of_contents or len(table_of_contents) == 0:
            return jsonify({"error": "Failed to generate table of contents"}), 500

        return (
            jsonify({"table_of_contents": table_of_contents, "toc_type": toc_type}),
            200,
        )

    except Exception as e:
        print(f"Error in TOC generation: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/hero-image", methods=["POST"])
@require_api_key
def generate_hero_image():
    """
    Generate hero image for blog content using either OpenAI GPT-Image-1 or Leonardo AI.

    Request JSON:
    {
        "table_of_contents": "Table of contents string",
        "force_provider": "openai" | "leonardo" (optional)
    }

    Response JSON:
    {
        "hero_image_url": "Generated image URL",
        "provider_used": "openai" | "leonardo"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        table_of_contents = data.get("table_of_contents", "").strip()
        force_provider = data.get("force_provider", "").lower()

        if not table_of_contents:
            return jsonify({"error": "Table of contents cannot be empty"}), 400

        # Determine which provider to use
        use_openai = should_use_openai_images()
        provider_used = "openai"

        # Allow forcing a specific provider via request parameter
        if force_provider == "leonardo":
            use_openai = False
            provider_used = "leonardo"
        elif force_provider == "openai":
            use_openai = True
            provider_used = "openai"
        else:
            provider_used = get_image_generation_mode()

        print(f"🎨 Generating hero image using {provider_used.upper()} provider")

        # Generate hero image using selected provider
        if use_openai:
            try:
                hero_image_url = generate_hero_image_openai(table_of_contents)
            except Exception as openai_error:
                print(f"❌ OpenAI generation failed: {str(openai_error)}")
                # Fallback to Leonardo if OpenAI fails
                print("🔄 Falling back to Leonardo AI...")
                hero_image_url = hero_image_generation(table_of_contents)
                provider_used = "leonardo"
        else:
            hero_image_url = hero_image_generation(table_of_contents)

        return (
            jsonify({"hero_image_url": hero_image_url, "provider_used": provider_used}),
            200,
        )

    except Exception as e:
        print(f"❌ Error in hero image generation: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/sonnet", methods=["POST"])
@require_api_key
def generate_sonnet():
    """
    Generate content using Claude Sonnet.

    Request JSON:
    {
        "prompt": "Text prompt for generation"
    }

    Response JSON:
    {
        "sonnet": "Generated text content"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        prompt = data.get("prompt")

        if not prompt:
            return jsonify({"error": "Missing required field: prompt"}), 400

        # Generate content using Claude Sonnet
        sonnet_content = generate_claude_sonnet(prompt)
        return jsonify({"sonnet": sonnet_content}), 200

    except Exception as e:
        print(f"Error in sonnet generation: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/chart", methods=["POST"])
@require_api_key
def generate_chart():
    """
    Generate chart/table for content.

    Request JSON:
    {
        "prompt": "Prompt for chart generation"
    }

    Response JSON:
    {
        "table_url": "Generated chart/table URL"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        prompt = data.get("prompt")

        if not prompt:
            return jsonify({"error": "Missing required field: prompt"}), 400

        # Generate chart
        table_url = table_chart_generation_new(prompt)
        return jsonify({"table_url": table_url}), 200

    except Exception as e:
        print(f"Error in chart generation: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/links", methods=["POST"])
@require_api_key
def analyze_links():
    """
    Analyze and fetch links for content.

    Request JSON:
    {
        "link_input": "Link or content to analyze",
        "link_type": "Type of link analysis"
    }

    Response JSON:
    {
        "results": "Analysis results"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        link_input = data.get("link_input")
        link_type = data.get("link_type")

        if not link_input or not link_type:
            return (
                jsonify({"error": "Missing required fields: link_input, link_type"}),
                400,
            )

        # Analyze links using MongoDB functionality
        results = fetch_entries_with_specific_link(link_input, link_type)
        return jsonify({"results": results}), 200

    except Exception as e:
        print(f"Error in link analysis: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/publish", methods=["POST"])
@require_api_key
def publish_to_wordpress():
    """
    Publish blog content to WordPress using the existing integration.

    Request JSON:
    {
        "content": "The main blog content in markdown",
        "title": "Blog post title",
        "toc": "Table of contents as string or list",
        "slug": "Optional pre-generated slug",
        "image_url": "Optional pre-generated hero image URL",
        "summary": "Optional blog summary",
        "meta_title": "Optional pre-generated meta title",
        "meta_description": "Optional pre-generated meta description"
    }

    Response JSON:
    {
        "status": "success|failed",
        "message": "Status message",
        "post_data": "WordPress post response data (if successful)"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()

        # Extract fields matching blog generation output format
        blog_content = data.get("content")
        title = data.get("title")
        table_of_contents = data.get("toc")

        # Optional pre-generated metadata
        pre_generated_slug = data.get("slug")
        pre_generated_image_url = data.get("image_url")
        pre_generated_meta_title = data.get("meta_title")
        pre_generated_meta_description = data.get("meta_description")
        pre_generated_summary = data.get("summary")  # Optional summary field

        # Validate required fields
        if not blog_content:
            return jsonify({"error": "Missing required field: content"}), 400
        if not title:
            return jsonify({"error": "Missing required field: title"}), 400
        if not table_of_contents:
            return jsonify({"error": "Missing required field: toc"}), 400

        # Convert table_of_contents to appropriate format if it's a list
        if isinstance(table_of_contents, list):
            table_of_contents = "\n".join(table_of_contents)

        # Publish to WordPress using the existing integration
        result = post_to_wordpress(
            blog_content,
            title,
            table_of_contents,
            pre_generated_slug,
            pre_generated_image_url,
            pre_generated_meta_title,
            pre_generated_meta_description,
            pre_generated_summary,
        )

        # Handle the response from post_to_wordpress
        if isinstance(result, tuple) and len(result) >= 2:
            status, post_data = result[0], result[1] if len(result) > 1 else None

            if "successful" in status.lower():
                return (
                    jsonify(
                        {"status": "success", "message": status, "post_data": post_data}
                    ),
                    200,
                )
            else:
                return (
                    jsonify(
                        {
                            "status": "failed",
                            "message": status,
                            "error_details": post_data,
                        }
                    ),
                    500,
                )
        else:
            # Handle unexpected response format
            return (
                jsonify(
                    {
                        "status": "failed",
                        "message": "Unexpected response from WordPress publishing",
                        "error_details": str(result),
                    }
                ),
                500,
            )

    except Exception as e:
        print(f"Error in WordPress publishing: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/slug", methods=["POST"])
@require_api_key
def generate_slug():
    """
    Generate a URL slug from a title.

    Request JSON:
    {
        "title": "Blog post title to convert to slug"
    }

    Response JSON:
    {
        "slug": "generated-url-slug"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        title = data.get("title")

        if not title:
            return jsonify({"error": "Missing required field: title"}), 400

        # Generate slug using the same function as exec.py
        slug = retry_on_529_error(slug_generator, title)

        if not slug:
            return jsonify({"error": "Failed to generate slug"}), 500

        return jsonify({"slug": slug}), 200

    except Exception as e:
        print(f"Error in slug generation: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/meta", methods=["POST"])
@require_api_key
def generate_meta():
    """
    Generate meta title and description from table of contents.

    Request JSON:
    {
        "table_of_contents": "Table of contents as string or list"
    }

    Response JSON:
    {
        "meta_title": "Generated SEO meta title",
        "meta_description": "Generated SEO meta description"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        table_of_contents = data.get("table_of_contents")

        if not table_of_contents:
            return jsonify({"error": "Missing required field: table_of_contents"}), 400

        # Convert table_of_contents to appropriate format if it's a list
        if isinstance(table_of_contents, list):
            table_of_contents = "\n".join(table_of_contents)

        # Generate meta title and description using the same function as exec.py
        content = retry_on_529_error(meta_description_and_title, table_of_contents)

        print(f"🔍 Raw AI Response: {repr(content)}")

        try:
            if "Title: " in content and "Description: " in content:
                # Parse title - get everything after "Title: " until double newline or end
                title_part = content.split("Title: ")[1]
                if "\n\n" in title_part:
                    meta_title = title_part.split("\n\n")[0].strip()
                else:
                    # If no double newline, take until single newline or end
                    meta_title = title_part.split("\n")[0].strip()

                # Parse description - get everything after "Description: "
                meta_description = content.split("Description: ")[1].strip()

                print(f"🔍 Parsed Title: {repr(meta_title)}")
                print(f"🔍 Parsed Description: {repr(meta_description)}")
            else:
                print(
                    f"🔍 Missing Title/Description markers in content: {repr(content)}"
                )
                print(f"🔍 Content contains 'Title: ': {'Title: ' in content}")
                print(
                    f"🔍 Content contains 'Description: ': {'Description: ' in content}"
                )
                raise ValueError("Missing 'Title:' or 'Description:' in content")
        except Exception as e:
            print(f"Error parsing meta content: {e}")
            print(f"🔍 Full content for debugging: {repr(content)}")
            return (
                jsonify({"error": f"Failed to parse generated meta content: {str(e)}"}),
                500,
            )

        return (
            jsonify({"meta_title": meta_title, "meta_description": meta_description}),
            200,
        )

    except Exception as e:
        print(f"Error in meta generation: {str(e)}")
        return jsonify({"error": str(e)}), 500


@api_bp.route("/content/summary", methods=["POST"])
@require_api_key
def generate_summary():
    """
    Generate a summary from blog content.

    Request JSON:
    {
        "content": "Blog content to summarize"
    }

    Response JSON:
    {
        "summary": "Generated summary of the content"
    }
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        content = data.get("content")

        if not content:
            return jsonify({"error": "Missing required field: content"}), 400

        # Generate summary using the same function as exec.py
        summary = get_summary(content)

        if not summary:
            return jsonify({"error": "Failed to generate summary"}), 500

        return jsonify({"summary": summary}), 200

    except Exception as e:
        print(f"Error in summary generation: {str(e)}")
        return jsonify({"error": str(e)}), 500
