
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Loader2, Search, RefreshCw } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { apiService } from "@/services/api";

interface SeoManagerProps {
  metaTitle: string;
  metaDescription: string;
  onSeoUpdate: (metaTitle: string, metaDescription: string) => void;
  topic?: string;
  blogContent?: string;
  toc?: string | string[];
}

export const SeoManager = ({
  metaTitle,
  metaDescription,
  onSeoUpdate,
  topic = "",
  blogContent = "",
  toc = ""
}: SeoManagerProps) => {
  const [isGenerating, setIsGenerating] = useState(false);

  // Use props directly instead of local state for better synchronization
  const currentMetaTitle = metaTitle;
  const currentMetaDescription = metaDescription;

  // Debug logging to see what props we're receiving
  console.log('🔍 SeoManager Props:', {
    metaTitle: `"${metaTitle}"`,
    metaDescription: `"${metaDescription}"`,
    titleLength: metaTitle.length,
    descLength: metaDescription.length
  });

  const generateSeoContent = async () => {
    // Check if we have either topic or toc to generate SEO content
    const tocToUse = toc || topic;
    if (!tocToUse || (typeof tocToUse === 'string' && !tocToUse.trim())) {
      toast({
        title: "Content Required",
        description: "Please provide a topic or table of contents to generate SEO content.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Use the real API to generate SEO content
      const tocArray = typeof tocToUse === 'string'
        ? tocToUse.split('\n').filter(item => item.trim() !== '')
        : tocToUse;

      const response = await apiService.generateMeta(tocArray);

      if (response.success && response.data) {
        const newMetaTitle = response.data.meta_title || '';
        const newMetaDescription = response.data.meta_description || '';

        console.log('🔍 SEO API Response:', response.data);
        console.log('🔍 SEO Generated:', {
          newMetaTitle: `"${newMetaTitle}"`,
          newMetaDescription: `"${newMetaDescription}"`,
          titleLength: newMetaTitle.length,
          descLength: newMetaDescription.length
        });

        // Update parent state - this should trigger a re-render with new props
        onSeoUpdate(newMetaTitle, newMetaDescription);

        console.log('🔍 SEO Parent Updated');

        toast({
          title: "SEO Content Generated",
          description: "Meta title and description have been generated successfully!",
        });
      } else {
        throw new Error(response.data?.error || response.message || "Failed to generate SEO content");
      }
    } catch (error) {
      console.error('SEO generation error:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate SEO content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleMetaTitleChange = (value: string) => {
    onSeoUpdate(value, currentMetaDescription);
  };

  const handleMetaDescriptionChange = (value: string) => {
    onSeoUpdate(currentMetaTitle, value);
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Search className="h-5 w-5" />
          <span>SEO Settings</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="meta-title">Meta Title ({currentMetaTitle.length}/60)</Label>
          <Input
            id="meta-title"
            key={`meta-title-${currentMetaTitle}`}
            value={currentMetaTitle}
            onChange={(e) => {
              console.log('🔍 Meta Title Input Changed:', e.target.value);
              handleMetaTitleChange(e.target.value);
            }}
            placeholder="Enter meta title"
          />
          {currentMetaTitle.length > 60 && (
            <p className="text-sm text-red-500">Meta title should be under 60 characters</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="meta-description">Meta Description ({currentMetaDescription.length}/160)</Label>
          <Textarea
            id="meta-description"
            key={`meta-description-${currentMetaDescription}`}
            value={currentMetaDescription}
            onChange={(e) => handleMetaDescriptionChange(e.target.value)}
            placeholder="Enter meta description"
            rows={3}
            maxLength={160}
            className="resize-none"
          />
          {currentMetaDescription.length > 160 && (
            <p className="text-sm text-red-500">Meta description should be under 160 characters</p>
          )}
        </div>

        <Button
          onClick={generateSeoContent}
          disabled={isGenerating || (!topic.trim() && (!toc || (typeof toc === 'string' && !toc.trim())))}
          className="w-full"
        >
          {isGenerating ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Generating SEO...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4 mr-2" />
              Regenerate SEO
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};
