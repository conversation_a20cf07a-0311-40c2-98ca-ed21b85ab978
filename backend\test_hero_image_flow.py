#!/usr/bin/env python3
"""
Test script to verify the complete hero image generation flow.
This script tests both OpenAI and Leonardo providers.
"""

import os
import sys
import time
import traceback
from dotenv import load_dotenv

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Load environment variables
load_dotenv()

def test_openai_flow():
    """Test the OpenAI hero image generation flow."""
    print("\n" + "="*60)
    print("🧪 TESTING OPENAI HERO IMAGE GENERATION FLOW")
    print("="*60)
    
    try:
        # Import after setting up the path
        from app.core.publishing.hero_images_openai import generate_hero_image_openai
        from app.utils.config import should_use_openai_images, get_image_generation_mode
        
        # Test configuration
        print(f"📋 OpenAI Images Enabled: {should_use_openai_images()}")
        print(f"📋 Current Image Generation Mode: {get_image_generation_mode()}")
        
        # Test table of contents
        test_toc = """
        Introduction to Artificial Intelligence in Education
        - Understanding AI fundamentals
        - Applications in modern learning
        
        Machine Learning for Educational Analytics
        - Student performance prediction
        - Personalized learning paths
        
        Future of AI in Higher Education
        - Emerging technologies
        - Implementation strategies
        """
        
        print(f"\n📝 Test Table of Contents:")
        print(test_toc.strip())
        
        print(f"\n🚀 Starting OpenAI hero image generation...")
        print("⚠️  This may take several minutes...")
        
        start_time = time.time()
        
        # Test with reference image (image-to-image)
        print("\n🖼️ Testing with reference image (image-to-image mode)...")
        result_with_ref = generate_hero_image_openai(test_toc, use_reference_image=True)
        
        end_time = time.time()
        
        print(f"\n✅ OpenAI hero image generated successfully!")
        print(f"🔗 Image URL: {result_with_ref}")
        print(f"⏱️  Generation time: {end_time - start_time:.2f} seconds")
        
        # Test without reference image (text-to-image)
        print("\n🎨 Testing without reference image (text-to-image mode)...")
        start_time = time.time()
        
        result_no_ref = generate_hero_image_openai(test_toc, use_reference_image=False)
        
        end_time = time.time()
        
        print(f"\n✅ Text-to-image generation successful!")
        print(f"🔗 Image URL: {result_no_ref}")
        print(f"⏱️  Generation time: {end_time - start_time:.2f} seconds")
        
        return True, [result_with_ref, result_no_ref]
        
    except Exception as e:
        print(f"\n❌ Error in OpenAI flow: {str(e)}")
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False, None

def test_leonardo_flow():
    """Test the Leonardo hero image generation flow."""
    print("\n" + "="*60)
    print("🧪 TESTING LEONARDO HERO IMAGE GENERATION FLOW")
    print("="*60)
    
    try:
        # Import after setting up the path
        from app.core.publishing.hero_images import hero_image_generation
        
        # Test table of contents
        test_toc = """
        Introduction to Digital Transformation in Education
        - Technology integration strategies
        - Digital learning platforms
        
        Student Engagement in Online Learning
        - Interactive content creation
        - Virtual collaboration tools
        
        Assessment and Analytics
        - Real-time feedback systems
        - Learning outcome measurement
        """
        
        print(f"\n📝 Test Table of Contents:")
        print(test_toc.strip())
        
        print(f"\n🚀 Starting Leonardo hero image generation...")
        print("⚠️  This may take several minutes...")
        
        start_time = time.time()
        result = hero_image_generation(test_toc)
        end_time = time.time()
        
        print(f"\n✅ Leonardo hero image generated successfully!")
        print(f"🔗 Image URL: {result}")
        print(f"⏱️  Generation time: {end_time - start_time:.2f} seconds")
        
        return True, result
        
    except Exception as e:
        print(f"\n❌ Error in Leonardo flow: {str(e)}")
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False, None

def test_api_endpoint():
    """Test the API endpoint with both providers."""
    print("\n" + "="*60)
    print("🧪 TESTING API ENDPOINT")
    print("="*60)
    
    try:
        import requests
        import json
        
        # API configuration
        api_base_url = "http://localhost:5000"
        api_key = os.getenv("IILM_API_KEY", "test_api_key")
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        test_payload = {
            "table_of_contents": "Introduction to AI\nMachine Learning Basics\nFuture Applications"
        }
        
        # Test with OpenAI
        print("\n🤖 Testing API endpoint with OpenAI provider...")
        openai_payload = {**test_payload, "force_provider": "openai"}
        
        response = requests.post(
            f"{api_base_url}/api/content/hero-image",
            headers=headers,
            data=json.dumps(openai_payload),
            timeout=300
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OpenAI API test successful!")
            print(f"🔗 Image URL: {data.get('hero_image_url')}")
            print(f"🏷️  Provider used: {data.get('provider_used')}")
        else:
            print(f"❌ OpenAI API test failed: {response.status_code} - {response.text}")
        
        # Test with Leonardo
        print("\n🎨 Testing API endpoint with Leonardo provider...")
        leonardo_payload = {**test_payload, "force_provider": "leonardo"}
        
        response = requests.post(
            f"{api_base_url}/api/content/hero-image",
            headers=headers,
            data=json.dumps(leonardo_payload),
            timeout=300
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Leonardo API test successful!")
            print(f"🔗 Image URL: {data.get('hero_image_url')}")
            print(f"🏷️  Provider used: {data.get('provider_used')}")
        else:
            print(f"❌ Leonardo API test failed: {response.status_code} - {response.text}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error in API endpoint test: {str(e)}")
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

def check_environment():
    """Check if all required environment variables are set."""
    print("\n" + "="*60)
    print("🔍 ENVIRONMENT CHECK")
    print("="*60)
    
    required_vars = [
        "OPENAI_API_KEY",
        "LEONARDO_API_KEY", 
        "AWS_ACCESS_KEY",
        "AWS_SECRET_KEY",
        "AWS_BUCKET_NAME",
        "AWS_REGION"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"❌ {var}: NOT SET")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Some tests may fail without proper configuration.")
        return False
    else:
        print("\n✅ All environment variables are set")
        return True

def main():
    """Main test function."""
    print("🧪 HERO IMAGE GENERATION FLOW TEST")
    print("="*60)
    
    # Check environment
    env_ok = check_environment()
    
    # Test OpenAI flow
    if os.getenv("OPENAI_API_KEY"):
        openai_success, openai_results = test_openai_flow()
    else:
        print("\n⚠️  Skipping OpenAI tests - OPENAI_API_KEY not set")
        openai_success = False
    
    # Test Leonardo flow
    if os.getenv("LEONARDO_API_KEY"):
        leonardo_success, leonardo_result = test_leonardo_flow()
    else:
        print("\n⚠️  Skipping Leonardo tests - LEONARDO_API_KEY not set")
        leonardo_success = False
    
    # Test API endpoint (requires server to be running)
    print("\n⚠️  API endpoint test requires the server to be running on localhost:5000")
    api_test = input("Do you want to test the API endpoint? (y/n): ").lower().strip() == 'y'
    
    if api_test:
        api_success = test_api_endpoint()
    else:
        api_success = None
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    print(f"🔧 Environment Setup: {'✅ PASS' if env_ok else '❌ FAIL'}")
    print(f"🤖 OpenAI Flow: {'✅ PASS' if openai_success else '❌ FAIL/SKIP'}")
    print(f"🎨 Leonardo Flow: {'✅ PASS' if leonardo_success else '❌ FAIL/SKIP'}")
    if api_test:
        print(f"🌐 API Endpoint: {'✅ PASS' if api_success else '❌ FAIL'}")
    else:
        print(f"🌐 API Endpoint: ⏭️  SKIPPED")
    
    if openai_success or leonardo_success:
        print("\n🎉 Hero image generation flow is working!")
    else:
        print("\n⚠️  Some issues detected. Check the logs above.")

if __name__ == "__main__":
    main()
