import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Save, X, Edit3, FileText, Hash } from "lucide-react";
import { GeneratedBlog } from "@/pages/Index";
import { toast } from "@/hooks/use-toast";
import { HeroImageManager } from "./HeroImageManager";
import { SeoManager } from "./SeoManager";

interface BlogEditProps {
  blog: GeneratedBlog;
  onSave: (updatedBlog: GeneratedBlog) => void;
  onCancel: () => void;
}

export const BlogEdit = ({ blog, onSave, onCancel }: BlogEditProps) => {
  const [editedBlog, setEditedBlog] = useState<GeneratedBlog>({ ...blog });
  const [tocString, setTocString] = useState(blog.toc.join('\n'));

  const handleSave = () => {
    const updatedBlog: GeneratedBlog = {
      ...editedBlog,
      toc: tocString.split('\n').filter(item => item.trim() !== '')
    };

    console.log("Saving edited blog:", updatedBlog);
    toast({
      title: "Blog Updated",
      description: "Your changes have been saved successfully!",
    });
    onSave(updatedBlog);
  };

  const updateField = (field: keyof GeneratedBlog, value: string) => {
    setEditedBlog(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImageUpdate = (imageUrl: string) => {
    updateField("image_url", imageUrl);
  };

  const handleSeoUpdate = (metaTitle: string, metaDescription: string) => {
    console.log('🔍 BlogEdit handleSeoUpdate called:', { metaTitle, metaDescription });
    setEditedBlog(prev => {
      const updated = {
        ...prev,
        meta_title: metaTitle,
        meta_description: metaDescription
      };
      console.log('🔍 BlogEdit state updated:', {
        oldTitle: prev.meta_title,
        newTitle: updated.meta_title,
        oldDesc: prev.meta_description,
        newDesc: updated.meta_description
      });
      return updated;
    });
  };

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 flex items-center space-x-2">
            <Edit3 className="h-8 w-8" />
            <span>Edit Blog Post</span>
          </h2>
          <p className="text-gray-600 mt-1">Make changes to your generated blog content</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={onCancel} className="flex items-center space-x-2">
            <X className="h-4 w-4" />
            <span>Cancel</span>
          </Button>
          <Button onClick={handleSave} className="flex items-center space-x-2 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
            <Save className="h-4 w-4" />
            <span>Save Changes</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Main Content Editor */}
        <div className="xl:col-span-3 space-y-6">
          {/* Basic Information */}
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">Blog Title</Label>
                <Input
                  id="title"
                  value={editedBlog.title}
                  onChange={(e) => updateField("title", e.target.value)}
                  className="text-lg font-semibold"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">
                  <div className="flex items-center space-x-1">
                    <Hash className="h-4 w-4" />
                    <span>URL Slug</span>
                  </div>
                </Label>
                <Input
                  id="slug"
                  value={editedBlog.slug}
                  onChange={(e) => updateField("slug", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="summary">Post Summary</Label>
                <Textarea
                  id="summary"
                  value={editedBlog.summary}
                  onChange={(e) => updateField("summary", e.target.value)}
                  rows={3}
                  className="resize-none"
                  placeholder="A brief summary of your blog post..."
                />
              </div>
            </CardContent>
          </Card>

          {/* Content Editor */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg">Blog Content</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-2">
                <Label htmlFor="content">Main Content</Label>
                <Textarea
                  id="content"
                  value={editedBlog.content}
                  onChange={(e) => updateField("content", e.target.value)}
                  rows={25}
                  className="font-mono text-sm resize-none"
                  placeholder="Write your blog content here..."
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="xl:col-span-1 space-y-6">
          {/* Hero Image Manager */}
          <HeroImageManager
            currentImageUrl={editedBlog.image_url}
            onImageUpdate={handleImageUpdate}
            topic={editedBlog.title}
            toc={tocString}
          />

          {/* SEO Manager */}
          <SeoManager
            metaTitle={editedBlog.meta_title}
            metaDescription={editedBlog.meta_description}
            onSeoUpdate={handleSeoUpdate}
            topic={editedBlog.title}
            blogContent={editedBlog.content}
            toc={tocString}
          />

          {/* Table of Contents */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-lg">Table of Contents</CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-2">
                <Label htmlFor="toc">Sections (one per line)</Label>
                <Textarea
                  id="toc"
                  value={tocString}
                  onChange={(e) => setTocString(e.target.value)}
                  rows={8}
                  className="resize-none text-sm"
                  placeholder="Introduction&#10;Chapter 1&#10;Chapter 2&#10;Conclusion"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
