
import { useState } from "react";
import { BlogInputData, GeneratedBlog } from "@/pages/Index";
import { apiService } from "@/services/api";
import { toast } from "@/hooks/use-toast";

type Step = "input" | "preview" | "edit" | "publish";

export const useBlogGeneration = () => {
  const [currentStep, setCurrentStep] = useState<Step>("input");
  const [inputData, setInputData] = useState<BlogInputData | null>(null);
  const [generatedBlog, setGeneratedBlog] = useState<GeneratedBlog | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [missingFields, setMissingFields] = useState<string[]>([]);

  const handleFormSubmit = async (data: BlogInputData) => {
    setInputData(data);
    setIsGenerating(true);

    console.log("Generating blog with data:", data);

    try {
      const response = await apiService.generateBlog(data);
      console.log("Full API response:", response);
      console.log("Response status:", response.status);
      console.log("Response data:", response.data);

      if ((response.success || response.status === 'partial') && response.data) {
        const blog = response.data;
        console.log("Blog data received:", blog);

        // Convert input TOC string to array format
        const inputTocArray = data.table_of_contents 
          ? data.table_of_contents.split('\n').filter(item => item.trim() !== '')
          : [];

        // Identify missing fields - only mark as missing if truly empty or null
        const missing: string[] = [];
        if (!blog.content || blog.content.trim() === '') {
          console.log("Missing content:", blog.content);
          missing.push('content');
        }
        if (!blog.title || blog.title.trim() === '') {
          console.log("Missing title:", blog.title);
          missing.push('title');
        }
        if (!blog.summary || blog.summary.trim() === '') {
          console.log("Missing summary:", blog.summary);
          missing.push('summary');
        }
        
        // Use input TOC if available, otherwise check if backend generated one
        const finalToc = inputTocArray.length > 0 ? inputTocArray : (blog.toc || []);
        if (finalToc.length === 0) {
          console.log("Missing TOC. Input TOC:", inputTocArray, "Backend TOC:", blog.toc);
          missing.push('table of contents');
        }
        
        if (!blog.image_url || blog.image_url.trim() === '') {
          console.log("Missing image_url:", blog.image_url);
          missing.push('hero image');
        }
        
        // Only mark meta fields as missing if they are truly empty
        if (!blog.meta_title || blog.meta_title.trim() === '') {
          console.log("Missing meta_title:", blog.meta_title);
          missing.push('meta title');
        }
        if (!blog.meta_description || blog.meta_description.trim() === '') {
          console.log("Missing meta_description:", blog.meta_description);
          missing.push('meta description');
        }

        console.log("Missing fields detected:", missing);
        setMissingFields(missing);

        // Transform API response to match our GeneratedBlog interface - no dummy values
        const transformedBlog: GeneratedBlog = {
          title: blog.title || "",
          summary: blog.summary || "",
          content: blog.content || "",
          toc: finalToc,
          slug: blog.slug || "",
          image_url: blog.image_url || "",
          meta_title: blog.meta_title || "",
          meta_description: blog.meta_description || ""
        };

        console.log("Final transformed blog:", transformedBlog);

        setGeneratedBlog(transformedBlog);
        setCurrentStep("preview");

        // Show appropriate toast based on response status
        if (response.status === 'partial' && missing.length > 0) {
          toast({
            title: "Blog Partially Generated",
            description: `Some fields need attention: ${missing.join(', ')}. You can complete them in the edit section.`,
            variant: "destructive",
          });
        } else {
          toast({
            title: "Blog Generated Successfully!",
            description: "Your blog post has been created. You can now preview and edit it.",
          });
        }
      } else {
        console.error("Invalid response structure:", response);
        throw new Error(response.error || "Failed to generate blog - invalid response structure");
      }
    } catch (error) {
      console.error("Blog generation error:", error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate blog. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleEdit = () => {
    setCurrentStep("edit");
  };

  const handleSaveEdits = (updatedBlog: GeneratedBlog) => {
    setGeneratedBlog(updatedBlog);
    setCurrentStep("preview");
  };

  const handlePublish = () => {
    setCurrentStep("publish");
  };

  const handleBackToInput = () => {
    setCurrentStep("input");
    setInputData(null);
    setGeneratedBlog(null);
    setMissingFields([]);
  };

  const handleBackToPreview = () => {
    setCurrentStep("preview");
  };

  return {
    currentStep,
    inputData,
    generatedBlog,
    isGenerating,
    missingFields,
    handleFormSubmit,
    handleEdit,
    handleSaveEdits,
    handlePublish,
    handleBackToInput,
    handleBackToPreview
  };
};
