
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Target } from "lucide-react";
import { BlogInputData } from "@/pages/Index";

interface TopicEmailSectionProps {
  formData: BlogInputData;
  onFormDataChange: (data: BlogInputData) => void;
}

export const TopicEmailSection = ({ formData, onFormDataChange }: TopicEmailSectionProps) => {
  return (
    <Card className="group border-0 bg-white/60 backdrop-blur-sm hover:bg-white/80 transition-all duration-500 hover:shadow-xl hover:shadow-blue-500/10 animate-fade-in w-full" style={{
      animationDelay: '0.1s'
    }}>
      <CardHeader className="pb-2 md:pb-3 px-3 sm:px-4 md:px-6">
        <div className="flex items-center space-x-2 md:space-x-3">
          <div className="p-1.5 md:p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg text-white group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
            <Target className="h-3 w-3 md:h-4 md:w-4 lg:h-5 lg:w-5" />
          </div>
          <div className="min-w-0 flex-1">
            <CardTitle className="text-base md:text-lg lg:text-xl truncate">Topic & Contact</CardTitle>
            <CardDescription className="text-xs md:text-sm">Define your blog topic and delivery preferences</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2 md:space-y-3 px-3 sm:px-4 md:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-3 lg:gap-4">
          <div className="space-y-1.5 md:space-y-2 min-w-0">
            <Label htmlFor="topic" className="text-xs md:text-sm font-medium">Blog Topic *</Label>
            <Input 
              id="topic" 
              placeholder="e.g., Machine Learning for Beginners" 
              value={formData.topic} 
              onChange={e => onFormDataChange({
                ...formData,
                topic: e.target.value
              })} 
              required 
              className="border-gray-200/50 bg-white/50 backdrop-blur-sm focus:bg-white/80 transition-all duration-300 text-sm md:text-base w-full" 
            />
          </div>
          <div className="space-y-1.5 md:space-y-2 min-w-0">
            <Label htmlFor="email" className="text-xs md:text-sm font-medium">Email Address *</Label>
            <Input 
              id="email" 
              type="email" 
              placeholder="<EMAIL>" 
              value={formData.receiver_email} 
              onChange={e => onFormDataChange({
                ...formData,
                receiver_email: e.target.value
              })} 
              required 
              className="border-gray-200/50 bg-white/50 backdrop-blur-sm focus:bg-white/80 transition-all duration-300 text-sm md:text-base w-full" 
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
