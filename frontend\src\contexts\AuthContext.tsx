
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { setCookie, getCookie, deleteCookie } from '@/utils/cookieUtils';

interface User {
  id: string;
  email: string;
  name?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored token on app load - prioritize cookies, fallback to localStorage
    const cookieToken = getCookie('auth_token');
    const cookieUser = getCookie('auth_user');
    const localToken = localStorage.getItem('auth_token');
    const localUser = localStorage.getItem('auth_user');
    
    const storedToken = cookieToken || localToken;
    const storedUser = cookieUser || localUser;
    
    console.log('Auth initialization - Cookie token:', !!cookieToken, 'Local token:', !!localToken);
    
    if (storedToken && storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setToken(storedToken);
        setUser(parsedUser);
        
        // Sync between cookie and localStorage if needed
        if (cookieToken && !localToken) {
          localStorage.setItem('auth_token', storedToken);
          localStorage.setItem('auth_user', storedUser);
        } else if (!cookieToken && localToken) {
          setCookie('auth_token', storedToken);
          setCookie('auth_user', storedUser);
        }
        
        console.log('User authenticated from stored credentials:', parsedUser.email);
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        // Clear both storage methods if parsing fails
        localStorage.removeItem('auth_token');
        localStorage.removeItem('auth_user');
        deleteCookie('auth_token');
        deleteCookie('auth_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // Mock login - replace with actual API call
      const response = await mockLogin(email, password);
      
      setToken(response.token);
      setUser(response.user);
      
      // Store in both localStorage and cookies
      localStorage.setItem('auth_token', response.token);
      localStorage.setItem('auth_user', JSON.stringify(response.user));
      setCookie('auth_token', response.token, 7); // 7 days
      setCookie('auth_user', JSON.stringify(response.user), 7); // 7 days
      
      console.log('User logged in successfully:', response.user.email);
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    
    // Clear both storage methods
    localStorage.removeItem('auth_token');
    localStorage.removeItem('auth_user');
    deleteCookie('auth_token');
    deleteCookie('auth_user');
    
    console.log('User logged out');
  };

  const mockLogin = async (email: string, password: string) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock validation - replace with actual API call
    if (email === '<EMAIL>' && password === 'password') {
      return {
        token: 'mock-jwt-token-' + Date.now(),
        user: {
          id: '1',
          email: email,
          name: 'Demo User'
        }
      };
    } else {
      throw new Error('Invalid email or password');
    }
  };

  const value = {
    user,
    token,
    login,
    logout,
    isLoading,
    isAuthenticated: !!user && !!token
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
