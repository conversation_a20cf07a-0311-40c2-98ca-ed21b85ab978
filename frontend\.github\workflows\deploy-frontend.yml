name: Deploy Frontend to EC2

on:
  push:
    branches:
      - dev

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Inject environment variables
      run: |
        echo "VITE_API_BASE_URL=${{ secrets.VITE_API_BASE_URL }}" > .env
        echo "VITE_API_KEY=${{ secrets.VITE_API_KEY }}" >> .env
        echo "VITE_NODE_ENV=${{ secrets.VITE_NODE_ENV }}" >> .env

    - name: Install Node modules and build
      run: |
        npm ci
        npm run build

    - name: Upload build to EC2
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USER }}
        key: ${{ secrets.EC2_SSH_KEY }}
        source: "./dist/"
        target: "/tmp/frontend-build"

    - name: SSH into EC2 and deploy
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.EC2_HOST }}
        username: ${{ secrets.EC2_USER }}
        key: ${{ secrets.EC2_SSH_KEY }}
        script: |
          sudo rm -rf /var/www/html/*
          sudo cp -r /tmp/frontend-build/dist/* /var/www/html/
          sudo chown -R www-data:www-data /var/www/html
          sudo chmod -R 755 /var/www/html
          sudo nginx -t && sudo systemctl reload nginx