import os
from autogen import <PERSON><PERSON><PERSON>
from pymongo import MongoClient
from app.utils.config import llm_config_claude as llm_config
from app.utils.helpers import get_sorted_links


def fetch_internal_link(blog_content, topic):
    """
    Generate search context for internal links based on blog content and topic.

    Args:
        blog_content (str): The blog content to analyze
        topic (str): The blog topic

    Returns:
        str: Search query string or empty string if failed
    """
    try:
        # Validate inputs
        if not blog_content or not blog_content.strip():
            print("Warning: Empty blog content provided to fetch_internal_link")
            return ""

        if not topic or not topic.strip():
            print("Warning: Empty topic provided to fetch_internal_link")
            return ""

        print("Generating search context for internal links...")

        # Improved system message for better context generation
        system_message = f"""
        Your task is to analyze the blog content and topic to generate a search query for finding related internal links.

        Topic: {topic}

        Instructions:
        1. Read the blog content carefully
        2. Identify the main themes, concepts, and keywords
        3. Generate a concise search query (3-8 words) that captures the core topics
        4. Focus on educational, business, or academic themes that would have related articles
        5. Output ONLY the search query text, nothing else

        Blog Content:
        {blog_content[:1000]}...
        """

        internal_context = AssistantAgent(
            name="InternalContext",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=4,
            system_message=system_message,
            llm_config=llm_config,
        )

        # Ensure blog_content is a string and convert to list of dictionaries
        if isinstance(blog_content, str):
            messages = [
                {"content": "Generate the search query as instructed.", "role": "user"}
            ]
        else:
            messages = blog_content

        response = internal_context.generate_reply(messages=messages)

        if response and isinstance(response, str) and response.strip():
            # Clean up the response to ensure it's a proper search query
            cleaned_response = response.strip().replace("\n", " ").replace("\r", " ")
            print(f"Generated search context: '{cleaned_response}'")
            return cleaned_response
        else:
            print("Warning: Failed to generate search context")
            return ""

    except Exception as e:
        print(f"Error in fetch_internal_link: {str(e)}")
        return ""


def fetch_service_link(blog_content, topic):
    internal_context = AssistantAgent(
        name="InternalContext",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=4,
        system_message=f"""
        Your task is to read about the section where IILM University is offering educational programs and services in the following content: {blog_content}
        and the {topic} and create a relevant text query relating to an educational program or service page in relation to the content.
        Make sure to include atleast one of the most relevant keyword from the list [programs, courses, admissions, education, university, degrees, learning]  and add to the query.
        Output these this text query only.
       """,  # topic, programs, courses, admissions, education, university, degrees, learning
        llm_config=llm_config,
        #    is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
    )
    # Ensure blog_content is a string and convert to list of dictionaries
    if isinstance(blog_content, str):
        messages = [{"content": blog_content, "role": "user"}]
    else:
        messages = blog_content  # Assume text is already a list of dictionaries

    response = internal_context.generate_reply(messages=messages)
    return response


# Singleton class for MongoDB client
class MongoSingleton:
    _client_instance = None

    @classmethod
    def get_client(cls):
        """
        Get MongoDB client instance with error handling.

        Returns:
            MongoClient: MongoDB client instance or None if connection fails
        """
        if cls._client_instance is None:
            try:
                mongo_uri = os.getenv("MONGO_URI")
                if not mongo_uri:
                    print("Error: MONGO_URI environment variable not set")
                    return None

                print("Initializing MongoDB connection...")
                # Try different SSL configurations for MongoDB Atlas
                try:
                    # First try with standard SSL
                    cls._client_instance = MongoClient(
                        mongo_uri,
                        serverSelectionTimeoutMS=10000,
                        connectTimeoutMS=10000,
                        socketTimeoutMS=10000,
                    )
                except Exception as ssl_error:
                    print(f"Standard connection failed: {ssl_error}")
                    # Try with SSL disabled (not recommended for production)
                    try:
                        cls._client_instance = MongoClient(
                            mongo_uri,
                            tls=False,
                            serverSelectionTimeoutMS=10000,
                            connectTimeoutMS=10000,
                            socketTimeoutMS=10000,
                        )
                    except Exception as no_ssl_error:
                        print(f"No-SSL connection also failed: {no_ssl_error}")
                        raise ssl_error  # Raise the original error

                # Test the connection
                cls._client_instance.admin.command("ping")
                print("MongoDB connection successful")

            except Exception as e:
                print(f"Error connecting to MongoDB: {str(e)}")
                cls._client_instance = None
                return None

        return cls._client_instance


def internal_link_function(context, collection1):
    """
    Fetch internal links from MongoDB collection based on text search context.

    Args:
        context (str): Search context/query
        collection1 (str): MongoDB collection name

    Returns:
        list: List of dictionaries with url and summary, empty list if no results or error
    """
    try:
        # Validate inputs
        if not context or not context.strip():
            print("Warning: Empty search context provided to internal_link_function")
            return []

        if not collection1:
            print("Warning: No collection name provided to internal_link_function")
            return []

        # Fetch MongoDB connection details from environment variables
        mongo_db_name = os.getenv("MONGO_DB_NAME")
        if not mongo_db_name:
            print("Error: MONGO_DB_NAME environment variable not set")
            return []

        print(
            f"Searching for internal links in collection: {collection1} with context: '{context[:50]}...'"
        )

        # Get the MongoClient instance via the singleton pattern
        client = MongoSingleton.get_client()
        if not client:
            print("Error: Failed to get MongoDB client")
            return []

        db = client[mongo_db_name]
        collection = db[collection1]

        # Check if collection exists and has documents
        doc_count = collection.count_documents({})
        if doc_count == 0:
            print(
                f"Info: Collection '{collection1}' is empty. Skipping internal link processing."
            )
            return []

        print(f"Found {doc_count} documents in collection '{collection1}'")

        # Define query for MongoDB text search
        query = {"$text": {"$search": f"{context}", "$caseSensitive": False}}

        # Execute the query and sort by relevance (textScore)
        documents = (
            collection.find(
                query,
                {
                    "url": 1,
                    "title": 1,
                    "summary": 1,
                    "_id": 0,
                    "score": {"$meta": "textScore"},
                },
            )
            .sort([("score", {"$meta": "textScore"})])
            .limit(100)
        )

        # Store URLs, titles, and summaries together in a dictionary for each document
        data = []
        for doc in documents:
            if doc.get("url"):  # Ensure URL exists
                data.append(
                    {
                        "url": doc["url"],
                        "summary": doc.get("summary", ""),
                        "title": doc.get("title", ""),
                    }
                )

        print(f"Found {len(data)} matching internal links")
        return data

    except Exception as e:
        print(f"Error in internal_link_function: {str(e)}")
        return []


def get_unique_new_links(fetched_links, link_list):
    # Convert link_list to a set for faster lookup
    existing_urls = set(link_list)

    # Create a new list with only the new links
    new_links = []
    for item in fetched_links:
        if item["url"] not in existing_urls:
            new_links.append(item)

    return new_links


def add_internal_link(blog_content, internal_link):
    """
    Add internal link to blog content using AI agent.

    Args:
        blog_content (str): The blog content to add links to
        internal_link (list): List of link dictionaries with url and summary

    Returns:
        str: Updated content with embedded link or original content if failed
    """
    try:
        # Validate inputs
        if not blog_content or not blog_content.strip():
            print("Warning: Empty blog content provided to add_internal_link")
            return blog_content

        if (
            not internal_link
            or not isinstance(internal_link, list)
            or len(internal_link) == 0
        ):
            print("Warning: No internal links provided to add_internal_link")
            return blog_content

        # Get the first link from the list
        link_data = internal_link[0]
        if not isinstance(link_data, dict) or "url" not in link_data:
            print("Warning: Invalid link data provided to add_internal_link")
            return blog_content

        link_url = link_data.get("url", "")
        link_title = link_data.get("title", "")
        link_summary = link_data.get("summary", "")

        print(f"Adding internal link: {link_url}")

        # Create improved system message with link details
        system_message = f"""
            Your task is to add ONE internal link to the blog content.

            Link to add:
            - URL: {link_url}
            - Title: {link_title}
            - Summary: {link_summary}

            Instructions:
            1. Find the most relevant keyword or phrase in the content that relates to this link
            2. Add the link in markdown format: [relevant text]({link_url})
            3. Only add the link ONCE in the entire content
            4. Do NOT link headings, titles, or subheadings
            5. Do NOT add or delete any other content
            6. Return the complete content with the embedded link

            Content to process:
            {blog_content}
        """

        internal_links_agent = AssistantAgent(
            name="InternalLinksAgent",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=4,
            system_message=system_message,
            llm_config=llm_config,
        )

        if isinstance(blog_content, str):
            messages = [
                {
                    "content": "Please add the internal link as instructed.",
                    "role": "user",
                }
            ]
        else:
            messages = blog_content

        response = internal_links_agent.generate_reply(messages=messages)

        if response and isinstance(response, str) and response.strip():
            print("Successfully generated content with internal link")
            return response
        else:
            print("Warning: AI agent returned empty or invalid response")
            return blog_content

    except Exception as e:
        print(f"Error in add_internal_link: {str(e)}")
        return blog_content


def add_service_link(blog_content, internal_link):
    internal_links_agent = AssistantAgent(
        name="InternalLinksAgent",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=4,
        system_message=f"""
            Your task is to read the section where IILM University is promoting educational programs and services and hyperlink the given
            {internal_link} on a relevant keyword in that section in the {blog_content}.
            Make sure to hyperlink the {internal_link} and do it once only in the given {blog_content}.
            Do not hyperlink on headings and titles.
            DO NOT add or delete the given content, Just embed the link in the content.
            Just Output the content in MD format with the embedded links .
        """,
        llm_config=llm_config,
        #    is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE"),
    )
    if isinstance(blog_content, str):
        messages = [{"content": blog_content, "role": "user"}]
    else:
        messages = blog_content  # Assume text is already a list of dictionaries

    response = internal_links_agent.generate_reply(messages=messages)
    return response


def get_internal_link(blog_content, link_list, topic):
    """
    Get internal links for blog content from BlogLinks collection.

    Args:
        blog_content (str): The blog content to add links to
        link_list (list): List of already used links to avoid duplicates
        topic (str): Blog topic for context generation

    Returns:
        tuple: (updated_content, links_added) or (original_content, None) if no links
    """
    try:
        print(f"Starting internal link processing for topic: {topic}")

        # Validate inputs
        if not blog_content or not blog_content.strip():
            print("Warning: Empty blog content provided to get_internal_link")
            return blog_content, None

        if not topic or not topic.strip():
            print("Warning: Empty topic provided to get_internal_link")
            return blog_content, None

        # Generate search context from blog content and topic
        print("Generating search context...")
        context = fetch_internal_link(blog_content, topic)
        if not context or not context.strip():
            print("Warning: Failed to generate search context")
            return blog_content, None

        print(f"Generated search context: '{context[:100]}...'")

        # Get MongoDB collection name
        mongo_collection_name = os.getenv("MONGO_COLLECTION_1")
        if not mongo_collection_name:
            print("Error: MONGO_COLLECTION_1 environment variable not set")
            return blog_content, None

        # Fetch links from MongoDB
        print(f"Fetching links from collection: {mongo_collection_name}")
        fetched_links = internal_link_function(context, mongo_collection_name)

        if not fetched_links:
            print("No internal links found in database - skipping link processing")
            return blog_content, None

        print(f"Found {len(fetched_links)} potential links")

        # Filter out already used links
        new_list = get_unique_new_links(fetched_links, link_list)
        if not new_list:
            print("All found links were already used - skipping link processing")
            return blog_content, None

        print(f"After filtering duplicates: {len(new_list)} unique links")

        # Sort links by relevance to content
        try:
            sorted_links = get_sorted_links(blog_content, new_list)
            if not sorted_links:
                print("No relevant links found after sorting")
                return blog_content, None
        except Exception as e:
            print(f"Error sorting links: {str(e)}")
            return blog_content, None

        # Take the top link (most relevant)
        top_link = sorted_links[0] if sorted_links else None
        if not top_link:
            print("No top link selected")
            return blog_content, None

        print(f"Selected top link: {top_link}")

        # Add the internal link to the content
        try:
            print("Adding internal link to content...")
            new_content = add_internal_link(blog_content, [top_link])
            if new_content and new_content != blog_content:
                print("Successfully added internal link to content")
                return new_content, [top_link]
            else:
                print("Failed to add internal link - content unchanged")
                return blog_content, None
        except Exception as e:
            print(f"Error adding internal link: {str(e)}")
            return blog_content, None

    except Exception as e:
        print(f"Error in get_internal_link: {str(e)}")
        return blog_content, None


def get_service_link(blog_content, link_list, topic):
    context = fetch_service_link(blog_content, topic)
    # json_context = json.loads(context)
    mongo_collection_name = os.getenv("MONGO_COLLECTION_2")

    fetched_links = internal_link_function(context, mongo_collection_name)

    if fetched_links:
        new_list = get_unique_new_links(fetched_links, link_list)
        sorted_links = get_sorted_links(blog_content, new_list)

    else:
        sorted_links = fetched_links

    first_link = sorted_links[0] if sorted_links else None
    print(first_link)

    if first_link:
        new_content = add_service_link(blog_content, first_link)

        return new_content, first_link
    return blog_content, None
