
import { useState } from "react";
import { BlogInputData } from "@/pages/Index";
import { BlogFormHeader } from "@/components/blog-input/BlogFormHeader";
import { TopicEmailSection } from "@/components/blog-input/TopicEmailSection";
import { ContentStructureSection } from "@/components/blog-input/ContentStructureSection";
import { AdvancedOptionsSection } from "@/components/blog-input/AdvancedOptionsSection";
import { BlogSubmitButton } from "@/components/blog-input/BlogSubmitButton";

interface BlogInputFormProps {
  onSubmit: (data: BlogInputData) => void;
  isGenerating: boolean;
}

export const BlogInputForm = ({
  onSubmit,
  isGenerating
}: BlogInputFormProps) => {
  const [formData, setFormData] = useState<BlogInputData>({
    topic: "",
    receiver_email: "",
    table_of_contents: "",
    blog_type: "informative",
    blog_length: "medium",
    include_image: "yes",
    internal_links: "no"
  });
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="w-full min-h-screen p-3 sm:p-4 md:p-6 lg:p-8 space-y-4 md:space-y-6 overflow-x-hidden">
      <BlogFormHeader />

      <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4 w-full max-w-4xl mx-auto">
        <TopicEmailSection 
          formData={formData} 
          onFormDataChange={setFormData} 
        />

        <ContentStructureSection 
          formData={formData} 
          onFormDataChange={setFormData} 
        />

        <AdvancedOptionsSection 
          formData={formData} 
          onFormDataChange={setFormData} 
        />

        <BlogSubmitButton 
          formData={formData} 
          isGenerating={isGenerating} 
        />
      </form>
    </div>
  );
};
