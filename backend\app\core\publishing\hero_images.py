import os
import json
import time
import requests
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
from autogen import AssistantAgent
from app.utils.config import llm_config, llm_config_claude
from app.utils.helpers import sanitize_text, upload_file_to_s3, get_random_image_path
import base64
import uuid
import mimetypes  # Import mimetypes to guess content type
from google.oauth2 import service_account
from google.cloud import storage
from d


def download_font_from_google_fonts(font_name="Montserrat", font_weight="700"):
    """
    Download font from Google Fonts API and save it locally.
    Returns the path to the downloaded font file.
    """
    try:
        # Create fonts directory if it doesn't exist
        fonts_dir = "app/core/content/dependencies/fonts"
        os.makedirs(fonts_dir, exist_ok=True)

        font_filename = f"{font_name}-{font_weight}.ttf"
        font_path = os.path.join(fonts_dir, font_filename)

        # Check if font already exists
        if os.path.exists(font_path):
            print(f"Font already exists: {font_path}")
            return font_path

        # Google Fonts API URL for Montserrat
        google_fonts_url = f"https://fonts.googleapis.com/css2?family={font_name}:wght@{font_weight}&display=swap"

        # Get the CSS file to extract the font URL
        response = requests.get(
            google_fonts_url,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
        )

        if response.status_code == 200:
            css_content = response.text
            # Extract the font URL from CSS
            import re

            font_url_match = re.search(
                r"url\((https://fonts\.gstatic\.com/[^)]+\.ttf)\)", css_content
            )

            if font_url_match:
                font_url = font_url_match.group(1)

                # Download the actual font file
                font_response = requests.get(font_url)
                if font_response.status_code == 200:
                    with open(font_path, "wb") as f:
                        f.write(font_response.content)
                    print(f"Font downloaded successfully: {font_path}")
                    return font_path
                else:
                    print(f"Failed to download font from: {font_url}")
            else:
                print("Could not extract font URL from CSS")
        else:
            print(f"Failed to get CSS from Google Fonts: {response.status_code}")

    except Exception as e:
        print(f"Error downloading font: {str(e)}")

    # Fallback to system default font
    return None


def get_font_path(font_name="Montserrat", font_weight="700", font_size=30):
    """
    Get the font path, downloading it if necessary.
    Returns a PIL ImageFont object.
    """
    try:
        # First try to get the downloaded font
        font_path = download_font_from_google_fonts(font_name, font_weight)

        if font_path and os.path.exists(font_path):
            return ImageFont.truetype(font_path, font_size)

        # Fallback options
        fallback_fonts = [
            "arial.ttf",
            "Arial.ttf",
            "calibri.ttf",
            "Calibri.ttf",
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux
        ]

        for fallback_font in fallback_fonts:
            try:
                return ImageFont.truetype(fallback_font, font_size)
            except:
                continue

        # Final fallback to default font
        print("Using default PIL font as fallback")
        return ImageFont.load_default()

    except Exception as e:
        print(f"Error loading font: {str(e)}")
        return ImageFont.load_default()


def divide_string(input_string):
    chunk_size = 20

    if len(input_string) <= chunk_size:
        return [input_string]
    substrings = []
    words = input_string.split()
    current_substring = ""
    current_length = 0

    for word in words:
        if current_length + len(word) > chunk_size:
            substrings.append(current_substring.strip())
            current_substring = ""
            current_length = 0

        current_substring += word + " "
        current_length += len(word) + 1

    if current_substring:
        substrings.append(current_substring.strip())
    return substrings


def text_for_image(text):
    hero_image_text_generator = AssistantAgent(
        name="HeroImageTextGenerator",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=4,
        system_message=f"""
        Create a captivating one-liner (maximum 60 characters) that summarizes the following table of content:
        {text}

        Your one-liner must:
        - Use powerful, evocative words that grab attention
        - Highlight the most intriguing or revolutionary aspect
        - Spark curiosity and encourage reading
        - Be concise, clear, and strictly within 60 characters
        - Optionally, use alliteration or wordplay

        🔴 Do not exceed 60 characters.
        Return only the one-liner, no explanations.
        """,
        llm_config=llm_config_claude,
        # is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE")
    )
    if isinstance(text, str):
        messages = [{"content": text, "role": "user"}]
    else:
        messages = text  # Assume greeting is already a list of dictionaries

    image_text = hero_image_text_generator.generate_reply(messages=messages)
    content_string = image_text["content"]
    return content_string


def add_text_to_image(title, image_bytes):
    old_text = text_for_image(title)
    text = old_text.replace('"', "")
    # Create output folder
    folder_name = "generated_images"
    os.makedirs(folder_name, exist_ok=True)

    # Load image
    image = image_bytes.convert("RGBA")
    draw = ImageDraw.Draw(image)
    width, height = image.size

    # === 1. Draw Navy Blue Left Strip ===
    blue_strip_width = int(width * 0.40)
    draw.rectangle(
        [(0, 0), (blue_strip_width, height)], fill=(0, 51, 153)
    )  # RGB Navy Blue

    # === 2. Load and Place White IILM Logo ===
    logo_path = (
        "app/core/content/dependencies/IILM-UNIVERSITY-NEW-LOGO-(WHITE) copy.png"
    )
    logo = Image.open(logo_path).convert("RGBA")
    logo_width = int(blue_strip_width * 0.8)
    logo_aspect = logo.height / logo.width
    logo = logo.resize((logo_width, int(logo_width * logo_aspect)))
    logo_x = int((blue_strip_width - logo_width) / 2)
    image.paste(logo, (logo_x, 170), logo)  # top padding

    # === 3. Responsive Maroon Boxes: Left-Aligned with Padding ===
    maroon_color = (140, 21, 21)
    words = title.split()
    text_lines = divide_string(text)
    current_line = ""
    top_start = 270

    # Layout controls
    font_size = 45
    line_spacing = 12
    box_padding_x = 30  # horizontal inside padding
    box_padding_y = 20  # vertical inside padding
    box_max_width = int(width * 0.9)  # box can go beyond blue strip
    font = get_font_path("Montserrat", "700", font_size)
    y_cursor = top_start

    for text in text_lines:
        # Wrap text manually based on width
        words = text.split()
        lines = []
        current_line = ""
        for word in words:
            test_line = current_line + " " + word if current_line else word
            test_width = draw.textlength(test_line, font=font)
            if test_width <= box_max_width - 2 * box_padding_x:
                current_line = test_line
            else:
                lines.append(current_line)
                current_line = word
        if current_line:
            lines.append(current_line)

        # Calculate box dimensions
        text_heights = [font.getbbox(line)[3] - font.getbbox(line)[1] for line in lines]
        total_text_height = sum(text_heights) + line_spacing * (len(lines) - 1)
        box_height = total_text_height + 2 * box_padding_y
        box_width = (
            max(draw.textlength(line, font=font) for line in lines) + 2 * box_padding_x
        )

        # Draw box (flush from left)
        box_left = 0
        box_right = box_left + box_width
        box_top = y_cursor
        box_bottom = y_cursor + box_height

        draw.rectangle(
            [(box_left, box_top), (box_right, box_bottom)], fill=maroon_color
        )

        # Draw text inside (padded left, vertically stacked)
        text_bboxes = [font.getbbox(line) for line in lines]
        text_heights = [(bbox[3] - bbox[1]) for bbox in text_bboxes]
        text_block_height = sum(text_heights) + line_spacing * (len(lines) - 1)

        # Adjust text Y position to fix centering visually
        vertical_nudge = 10  # More aggressive upward shift
        y_text = box_top + (box_height - text_block_height) // 2 - vertical_nudge

        for line in lines:
            bbox = font.getbbox(line)
            line_height = bbox[3] - bbox[1]
            draw.text(
                (box_left + box_padding_x, y_text),
                line,
                font=font,
                fill=(255, 255, 255),
            )
            y_text += line_height + line_spacing

        # Move cursor down for next box
        y_cursor += box_height + 20

    # === 4. Save Final Image ===
    sanitized_text = sanitize_text(title)
    final_image_path = f"{folder_name}/{sanitized_text}.jpg"
    image.convert("RGB").save(final_image_path, "JPEG", quality=95)

    image_filename = f"generated_images/{sanitized_text}.jpg"
    try:
        # Save the image with the text overlay
        image.convert("RGB").save(image_filename, "JPEG", quality=95)
        print(f"📁 Image saved locally: {image_filename}")

        # Upload to S3
        image_url = upload_file_to_s3(image_filename)

        # Clean up local file after upload
        if os.path.exists(image_filename):
            os.remove(image_filename)
            print(f"🗑️ Local file cleaned up: {image_filename}")

        if not image_url:
            raise RuntimeError("failed to upload hero image to S3")
        print(f"✅ hero image uploaded → {image_url}")
        return image_url
    except Exception as e:
        print(f"❌ Error saving/uploading image: {str(e)}")
        return False


def generate_prompt(text):
    template = """
        Generate a visually stunning, hyper-realistic, and SEO-optimized hero image for a prestigious university website focused on Educational Excellence and Innovation in Higher Education.

        The image must feature:
            - A vibrant, real-world depiction of modern university life with emphasis on authenticity (no text overlays or logos).
            - Focus on a bright, engaging, and visually rich scene primarily on the **right-hand side**, keeping the **left 35% of the frame uncluttered** and low-contrast for overlay content.

        Scene Description:
        [Scene Focus — e.g., a modern university campus with sunlit glass buildings, students collaborating on tablets, or a high-tech academic hub with greenery and learning spaces]. Avoid AI-looking artifacts and ensure photographic realism.

        Design Elements to Include:
            - Natural lighting, golden hour tones or soft diffused daylight
            - Depth of field to direct attention to the main focus on the right
            - Color palette: University blue, subtle academic gold, white, green accents
            - Architectural sharpness: clean lines, open space, organized layout
            - Human element: diverse students or faculty engaged in authentic, dynamic activities (e.g., discussion, walking, studying)
            - Subtle motion or life: leaves blowing, sunbeam through glass, screen glow
            - Optional elements: books, campus banners, innovation labs, digital whiteboards

        Exclusions:
            - No visible text, logos, or branding
            - No uncanny AI features (over-smoothed faces, over-saturation, etc.)

        Target Specifications:
            - Format: full-width responsive website hero
            - Aspect Ratio: 16:9 preferred
            - Usage: Homepage of a university website with a reputation for excellence

        Emotion to Evoke:
        - Trust, inspiration, innovation, and a global academic atmosphere

        Key Note:
        Ensure the image conveys an **aspirational yet grounded academic environment**, suitable for global branding and repeatable across departments or pages.
        """
    hero_image_generator = AssistantAgent(
        name="HeroImageGenerator",
        human_input_mode="NEVER",
        max_consecutive_auto_reply=4,
        system_message=f"""
        Your task is to generate a detailed prompt based on the following {template} to be used for realistic AI-generated university hero images. Do not include any placeholder text or ask questions. 
        The prompt should be appended to:
        "Keep the left 35% of the image simple and blurred while the right side showcases..."
        Use this context: {text}
        """,
        llm_config=llm_config,
        # is_termination_msg=lambda x: x.get("content", "").rstrip().endswith("TERMINATE")
    )
    if isinstance(text, str):
        messages = [{"content": text, "role": "user"}]
    else:
        messages = text  # Assume greeting is already a list of dictionaries
    prompt0 = f"Keep the left 35% of the image simple and blurred while the right side showcases"
    generated_text = hero_image_generator.generate_reply(messages=messages)

    # Handle both string and dict responses
    if isinstance(generated_text, dict):
        generated_content = generated_text.get("content", str(generated_text))
    else:
        generated_content = str(generated_text)

    prompt = prompt0 + generated_content

    if os.getenv("USE_OPENAI_IMAGES") == "false":
        # Apply hard limit of 1500 characters for Leonardo AI
        MAX_PROMPT_LENGTH = 1500
        if len(prompt) > MAX_PROMPT_LENGTH:
            print(
                f"⚠️  Prompt too long ({len(prompt)} chars), truncating to {MAX_PROMPT_LENGTH} chars"
            )
            # Truncate but try to end at a complete sentence
            truncated = prompt[:MAX_PROMPT_LENGTH]
            # Find the last period, exclamation, or question mark before the limit
            last_sentence_end = max(
                truncated.rfind("."), truncated.rfind("!"), truncated.rfind("?")
            )
            if last_sentence_end > len(
                prompt0
            ):  # Make sure we don't cut the base prompt
                prompt = truncated[: last_sentence_end + 1]
            else:
                # If no sentence ending found, just truncate at word boundary
                last_space = truncated.rfind(" ")
                if last_space > len(prompt0):
                    prompt = truncated[:last_space]
                else:
                    prompt = truncated

            print(f"✓ Prompt truncated to {len(prompt)} characters")

    return prompt


def hero_image_generation(text):
    # Check if 'text' is a list
    if isinstance(text, list):
        result = ",".join(text)
    else:
        result = text
    prompt = generate_prompt(result)
    print(prompt)
    api_key = os.getenv("LEONARDO_API_KEY")
    authorization = f"Bearer {api_key}"

    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "authorization": authorization,
    }

    # # Get a presigned URL for uploading an image
    url = "https://cloud.leonardo.ai/api/rest/v1/init-image"
    payload = {"extension": "jpg"}
    response = requests.post(url, json=payload, headers=headers)
    print("Get a presigned URL for uploading an image:", response.status_code)

    # # Upload image via presigned URL
    fields = json.loads(response.json()["uploadInitImage"]["fields"])
    url = response.json()["uploadInitImage"]["url"]

    # # For getting the image later
    image_id = response.json()["uploadInitImage"]["id"]
    image_file_path = get_random_image_path()
    files = {"file": open(image_file_path, "rb")}
    response = requests.post(url, data=fields, files=files)
    print("Upload image via presigned URL:", response.status_code)

    # Generate with Image to Image
    url = "https://cloud.leonardo.ai/api/rest/v1/generations"
    payload = {
        "height": 544,
        "num_images": 1,
        "modelId": "aa77f04e-3eec-4034-9c07-d0f619684628",  # Leonardo Kino XL
        "prompt": prompt,
        "width": 1024,
        "init_image_id": image_id,
        "init_strength": 0.35,
        "guidance_scale": 7,
        "highResolution": True,
    }

    response = requests.post(url, json=payload, headers=headers)
    print("Generation of Images using Image to Image:", response.status_code)
    print(response.text)

    # Check if the generation request was successful
    if response.status_code != 200:
        error_msg = f"Leonardo AI generation failed with status {response.status_code}: {response.text}"
        print(f"❌ {error_msg}")
        raise Exception(error_msg)

    # Get the generation of images
    response_data = response.json()
    if "sdGenerationJob" not in response_data:
        error_msg = f"Invalid response from Leonardo AI: {response.text}"
        print(f"❌ {error_msg}")
        raise Exception(error_msg)

    generation_id = response_data["sdGenerationJob"]["generationId"]
    url = f"https://cloud.leonardo.ai/api/rest/v1/generations/{generation_id}"

    # Poll for the result with exponential backoff
    max_attempts = 30  # Maximum number of attempts
    initial_wait = 1  # Start with 1 second wait
    max_wait = 5  # Maximum wait between attempts in seconds
    attempt = 0

    while attempt < max_attempts:
        response = requests.get(url, headers=headers)
        data = json.loads(response.text)

        # Check if generation is complete
        status = data.get("generations_by_pk", {}).get("status")
        print(f"Generation status: {status} (attempt {attempt+1}/{max_attempts})")

        if status == "COMPLETE":
            print("Generation complete!")
            break
        elif status == "FAILED":
            raise Exception("Image generation failed")

        # Calculate wait time with exponential backoff (capped at max_wait)
        wait_time = min(initial_wait * (2**attempt), max_wait)
        print(f"Waiting {wait_time} seconds before next check...")
        time.sleep(wait_time)
        attempt += 1

    if attempt >= max_attempts:
        raise Exception("Timed out waiting for image generation to complete")

    print(response.text)

    # Extract the URL from the parsed JSON
    image_url_1 = data["generations_by_pk"]["generated_images"][0]["url"]
    print(image_url_1)
    response = requests.get(image_url_1)
    image = Image.open(BytesIO(response.content))
    print(image)
    # image.save(f"{text}.jpg")
    image_url = add_text_to_image(result, image)
    return image_url
