name: Deploy Backend to EC2

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Prepare and compress backend source
        run: |
          mkdir backend_temp
          shopt -s extglob
          cp -r !(.git|backend.tar.gz|backend_temp) backend_temp/
          tar -czf backend.tar.gz -C backend_temp .

      - name: Upload to EC2
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          source: "backend.tar.gz"
          target: "~/apps/backend"

      - name: SSH into EC2 and deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            export AUTOGEN_USE_DOCKER=${{ secrets.AUTOGEN_USE_DOCKER }}
            export PATH="$HOME/.local/bin:$PATH"
            cd ~/apps/backend
            rm -rf ./backend_temp
            mkdir backend_temp
            mv backend.tar.gz backend_temp/
            tar -xzf backend_temp/backend.tar.gz -C backend_temp

            # Create .env file from GitHub secrets
            cat <<EOF > .env
            APP_NAME=${{ secrets.APP_NAME }}
            APP_ENV=${{ secrets.APP_ENV }}
            APP_DEBUG=${{ secrets.APP_DEBUG }}
            OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
            CLAUDE_API_KEY=${{ secrets.CLAUDE_API_KEY }}
            AWS_ACCESS_KEY=${{ secrets.AWS_ACCESS_KEY }}
            AWS_SECRET_KEY=${{ secrets.AWS_SECRET_KEY }}
            AWS_BUCKET_NAME=${{ secrets.AWS_BUCKET_NAME }}
            AWS_REGION=${{ secrets.AWS_REGION }}
            IILM_API_KEY=${{ secrets.IILM_API_KEY }}
            LEONARDO_API_KEY=${{ secrets.LEONARDO_API_KEY }}
            ERASER_API_KEY=${{ secrets.ERASER_API_KEY }}
            AUTOGEN_USE_DOCKER=${{ secrets.AUTOGEN_USE_DOCKER  }}
            TAVILY_API_KEY=${{ secrets.TAVILY_API_KEY }}
            WORDPRESS_API_URL=${{ secrets.WORDPRESS_API_URL }}
            WORDPRESS_USERNAME=${{ secrets.WORDPRESS_USERNAME }}
            WORDPRESS_APP_PASSWORD=${{ secrets.WORDPRESS_APP_PASSWORD }}
            EOF

            cd backend_temp/
            poetry install --no-interaction --no-root
            sudo kill -9 $(sudo lsof -t -i:5000) 2>/dev/null || true

            AUTOGEN_USE_DOCKER=False nohup poetry run python start_server.py > output.log 2>&1 &