
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, FileText, Zap, Info } from "lucide-react";
import { BlogInputData } from "@/pages/Index";
import { apiService } from "@/services/api";
import { toast } from "@/hooks/use-toast";

interface ContentStructureSectionProps {
  formData: BlogInputData;
  onFormDataChange: (data: BlogInputData) => void;
}

export const ContentStructureSection = ({ formData, onFormDataChange }: ContentStructureSectionProps) => {
  const [isGeneratingToc, setIsGeneratingToc] = useState(false);
  
  const handleGenerateToc = async () => {
    setIsGeneratingToc(true);
    try {
      const response = await apiService.generateToc(formData.topic);
      if (response.status === 'success' && response.data) {
        // Backend returns table_of_contents as an array, convert to string
        const tocArray = response.data.table_of_contents || [];
        const tocString = Array.isArray(tocArray) ? tocArray.join('\n') : tocArray;

        onFormDataChange({
          ...formData,
          table_of_contents: tocString
        });
        toast({
          title: "TOC Generated Successfully!",
          description: "A table of contents has been automatically generated for your blog post."
        });
      } else {
        throw new Error(response.message || "Failed to generate TOC");
      }
    } catch (error) {
      console.error("TOC generation error:", error);
      toast({
        title: "TOC Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate TOC. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingToc(false);
    }
  };

  return (
    <Card className="group border-0 bg-white/60 backdrop-blur-sm hover:bg-white/80 transition-all duration-500 hover:shadow-xl hover:shadow-emerald-500/10 animate-fade-in w-full" style={{
      animationDelay: '0.2s'
    }}>
      <CardHeader className="pb-2 md:pb-3 px-3 sm:px-4 md:px-6">
        <div className="flex items-center space-x-2 md:space-x-3">
          <div className="p-1.5 md:p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg text-white group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
            <FileText className="h-3 w-3 md:h-4 md:w-4 lg:h-5 lg:w-5" />
          </div>
          <div className="min-w-0 flex-1">
            <CardTitle className="text-base md:text-lg lg:text-xl truncate">Content Structure</CardTitle>
            <CardDescription className="text-xs md:text-sm">Customize your blog's format and organization</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3 md:space-y-4 px-3 sm:px-4 md:px-6">
        {/* Table of Contents */}
        <div className="space-y-1.5 md:space-y-2">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1.5 md:gap-2">
            <Label htmlFor="toc" className="text-xs md:text-sm font-medium">Table of Contents</Label>
            <Button type="button" variant="outline" size="sm" onClick={handleGenerateToc} disabled={!formData.topic || isGeneratingToc} className="h-7 md:h-8 text-xs bg-white/50 backdrop-blur-sm hover:bg-white/80 transition-all duration-300 w-full sm:w-auto flex-shrink-0">
              {isGeneratingToc ? <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Generating...
              </> : <>
                <Zap className="h-3 w-3 mr-1" />
                Auto-Generate
              </>}
            </Button>
          </div>
          <Textarea 
            id="toc" 
            placeholder="Leave empty for auto-generation or specify your outline..." 
            value={formData.table_of_contents} 
            onChange={e => onFormDataChange({
              ...formData,
              table_of_contents: e.target.value
            })} 
            className="min-h-[70px] md:min-h-[80px] lg:min-h-[100px] border-gray-200/50 bg-white/50 backdrop-blur-sm focus:bg-white/80 transition-all duration-300 text-xs md:text-sm w-full resize-none" 
          />
          
          {/* Info message about auto-generation */}
          <div className="flex items-start space-x-2 p-2 bg-blue-50/50 border border-blue-200/50 rounded-md">
            <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <p className="text-xs text-blue-700">
              Press <strong>Auto-Generate</strong> to create a Table of Contents based on your topic. If left empty, will be generated by us.
            </p>
          </div>
        </div>

        {/* Blog Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3 lg:gap-4">
          <div className="space-y-1.5 md:space-y-2 min-w-0">
            <Label className="text-xs md:text-sm font-medium">Blog Type</Label>
            <Select value={formData.blog_type} onValueChange={(value: "informative" | "technical") => onFormDataChange({
              ...formData,
              blog_type: value
            })}>
              <SelectTrigger className="border-gray-200/50 bg-white/50 backdrop-blur-sm focus:bg-white/80 transition-all duration-300 text-xs md:text-sm w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white/90 backdrop-blur-md border-gray-200/50">
                <SelectItem value="informative">Informative</SelectItem>
                <SelectItem value="technical">Technical</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-1.5 md:space-y-2 min-w-0">
            <Label className="text-xs md:text-sm font-medium">Blog Length</Label>
            <Select value={formData.blog_length} onValueChange={(value: "short" | "medium" | "long") => onFormDataChange({
              ...formData,
              blog_length: value
            })}>
              <SelectTrigger className="border-gray-200/50 bg-white/50 backdrop-blur-sm focus:bg-white/80 transition-all duration-300 text-xs md:text-sm w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white/90 backdrop-blur-md border-gray-200/50">
                <SelectItem value="short">Short (500-800 words)</SelectItem>
                <SelectItem value="medium">Medium (800-1500 words)</SelectItem>
                <SelectItem value="long">Long (1500+ words)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
