
import { BaseApiClient } from './apiClient';
import { ApiResponse, BlogGenerationRequest } from '../types/api';

export class BlogApiService extends BaseApiClient {
  async healthCheck(): Promise<ApiResponse<any>> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/health`, {
        headers: this.getHeaders()
      });
      const data = await response.json();
      return { success: true, status: 'success', data };
    } catch (error) {
      console.error('Health check failed:', error);
      return { success: false, status: 'error', message: 'Health check failed' };
    }
  }

  async generateBlog(blogData: BlogGenerationRequest): Promise<ApiResponse<any>> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/blog/generate`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(blogData)
      });

      const responseData = await response.json();
      console.log('Raw API response:', responseData);
      console.log('Response status:', response.status);

      if (response.ok || response.status === 207) {
        // Backend sends blog data in 'message' property, but frontend expects 'data'
        // Map the response structure to match frontend expectations
        return { 
          success: responseData.success,
          status: response.status === 207 ? 'partial' : 'success',
          data: responseData.message, // Backend sends data in 'message', frontend expects 'data'
          error: responseData.error
        };
      } else {
        return {
          success: false,
          status: 'error',
          error: responseData.error || responseData.message || 'Blog generation failed'
        };
      }
    } catch (error) {
      console.error('Blog generation failed:', error);
      return {
        success: false,
        status: 'error',
        error: 'Network error during blog generation'
      };
    }
  }
}

export const blogApiService = new BlogApiService();
