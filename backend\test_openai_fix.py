#!/usr/bin/env python3
"""
Quick test to verify OpenAI image generation fix.
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Load environment variables
load_dotenv()

def test_openai_fix():
    """Test the fixed OpenAI implementation."""
    print("🧪 Testing OpenAI Hero Image Generation Fix")
    print("="*50)
    
    try:
        from app.core.publishing.hero_images_openai import generate_hero_image_openai
        
        # Simple test content
        test_content = "Introduction to AI\nMachine Learning Basics\nFuture Applications"
        
        print(f"📝 Test content: {test_content}")
        print("🚀 Starting generation...")
        
        # Test with reference image (the failing case)
        result = generate_hero_image_openai(test_content, use_reference_image=True)
        
        print(f"✅ Success! Image URL: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_openai_fix()
    if success:
        print("\n🎉 OpenAI fix is working!")
    else:
        print("\n⚠️  Fix needs more work.")
