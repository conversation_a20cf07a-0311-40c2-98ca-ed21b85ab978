
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { UserMenu } from "./UserMenu";

interface BlogHeaderProps {
  showBackButton?: boolean;
  onBackToInput?: () => void;
}

export const BlogHeader = ({ showBackButton, onBackToInput }: BlogHeaderProps) => {
  return (
    <header className="relative z-20 bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {showBackButton && (
              <Button 
                variant="ghost" 
                size="sm"
                onClick={onBackToInput}
                className="text-gray-600 hover:text-gray-900"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Start
              </Button>
            )}
            
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/2b7b40f6-b2fd-445f-8ff1-b182ba721c63.png" 
                alt="IILM University Logo"
                className="h-10 w-auto"
              />
              <div>
                <p className="text-xs text-gray-600">Blog Generator Platform</p>
              </div>
            </div>
          </div>

          <UserMenu />
        </div>
      </div>
    </header>
  );
};
