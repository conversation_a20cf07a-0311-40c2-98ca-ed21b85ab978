
import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Image, Upload, RefreshCw } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { apiService } from "@/services/api";

interface HeroImageManagerProps {
  currentImageUrl?: string;
  onImageUpdate: (imageUrl: string) => void;
  topic?: string;
  toc?: string | string[];
}

export const HeroImageManager = ({ currentImageUrl = "", onImageUpdate, topic = "", toc = "" }: HeroImageManagerProps) => {
  const [imageUrl, setImageUrl] = useState(currentImageUrl);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateHeroImage = async () => {
    // Check if we have either topic or toc to generate hero image
    const tocToUse = toc || topic;
    if (!tocToUse || (typeof tocToUse === 'string' && !tocToUse.trim())) {
      toast({
        title: "Content Required",
        description: "Please provide a topic or table of contents to generate hero image.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Use the real API to generate hero image
      const tocArray = typeof tocToUse === 'string'
        ? tocToUse.split('\n').filter(item => item.trim() !== '')
        : tocToUse;

      const response = await apiService.generateHeroImage(tocArray);

      if (response.success && response.data) {
        const newImageUrl = response.data.hero_image_url || '';

        if (newImageUrl) {
          setImageUrl(newImageUrl);
          onImageUpdate(newImageUrl);

          toast({
            title: "Hero Image Generated",
            description: "New hero image has been generated successfully!",
          });
        } else {
          throw new Error("No image URL received from server");
        }
      } else {
        throw new Error(response.data?.error || response.message || "Failed to generate hero image");
      }
    } catch (error) {
      console.error('Hero image generation error:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate hero image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File",
        description: "Please upload an image file.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    try {
      // Convert file to URL (in real app, upload to your storage service)
      const uploadedImageUrl = URL.createObjectURL(file);

      setImageUrl(uploadedImageUrl);
      onImageUpdate(uploadedImageUrl);

      toast({
        title: "Image Uploaded",
        description: "Hero image has been uploaded successfully!",
      });
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: "Failed to upload image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleUrlUpdate = () => {
    onImageUpdate(imageUrl);
    toast({
      title: "Image Updated",
      description: "Hero image URL has been updated!",
    });
  };

  return (
    <Card className="border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Image className="h-5 w-5" />
          <span>Hero Image</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="image-url">Image URL</Label>
          <div className="flex space-x-2">
            <Input
              id="image-url"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="Enter image URL or upload a file"
            />
            <Button
              variant="outline"
              onClick={handleUrlUpdate}
              disabled={!imageUrl.trim()}
            >
              Update
            </Button>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            onClick={generateHeroImage}
            disabled={isGenerating || (!topic.trim() && (!toc || (typeof toc === 'string' && !toc.trim())))}
            className="flex-1"
            variant="outline"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Generating...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Generate New
              </>
            )}
          </Button>

          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="flex-1"
            variant="outline"
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </>
            )}
          </Button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileUpload}
          className="hidden"
        />

        {imageUrl && (
          <div className="space-y-2">
            <Label>Preview</Label>
            <div className="relative rounded-lg overflow-hidden border">
              <img
                src={imageUrl}
                alt="Hero image preview"
                className="w-full h-32 object-cover"
                onError={(e) => {
                  e.currentTarget.src = "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=200&fit=crop";
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
