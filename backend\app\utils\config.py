import os
from dotenv import load_dotenv

load_dotenv()

config_list = [
    {
        "model": "gpt-4o-mini",
        "api_key": os.getenv("OPENAI_API_KEY"),
        # 'base_url': os.getenv("BASE_URL")
    }
]

config_list_4 = [
    {
        "model": "gpt-4-turbo",
        "api_key": os.getenv("OPENAI_API_KEY"),
        # 'base_url': os.getenv("BASE_URL")
    }
]

config_list_claude = [
    {
        "model": "claude-3-5-sonnet-20240620",
        "api_key": os.getenv("CLAUDE_API_KEY"),
        "api_type": "anthropic",
    }
]

llm_config = {
    "timeout": 600,
    "cache_seed": None,
    "seed": 42,
    "config_list": config_list,
    "temperature": 0,
}

llm_config_claude = {
    "timeout": 600,
    "cache_seed": None,
    "seed": 42,
    "config_list": config_list_claude,
    "temperature": 0,
}

# OpenAI Image Generation Configuration
openai_image_config = {
    "use_openai_images": os.getenv("USE_OPENAI_IMAGES", "false").lower() == "true",
    "default_size": os.getenv("OPENAI_IMAGE_DEFAULT_SIZE", "1024x1024"),
    "default_quality": os.getenv("OPENAI_IMAGE_DEFAULT_QUALITY", "high"),
    "default_format": os.getenv("OPENAI_IMAGE_DEFAULT_FORMAT", "png"),
    "api_key": os.getenv("OPENAI_API_KEY"),
}


def get_image_generation_mode():
    """
    Get the current image generation mode (OpenAI or Leonardo).

    Returns:
        str: "openai" if OpenAI is enabled, "leonardo" otherwise
    """
    return "openai" if openai_image_config["use_openai_images"] else "leonardo"


def should_use_openai_images():
    """
    Check if OpenAI images should be used for hero image generation.

    Returns:
        bool: True if OpenAI should be used, False for Leonardo
    """
    return openai_image_config["use_openai_images"]
