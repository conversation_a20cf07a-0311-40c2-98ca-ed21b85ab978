
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Settings } from "lucide-react";
import { BlogInputData } from "@/pages/Index";
import { toast } from "@/hooks/use-toast";

interface AdvancedOptionsSectionProps {
  formData: BlogInputData;
  onFormDataChange: (data: BlogInputData) => void;
}

export const AdvancedOptionsSection = ({ formData, onFormDataChange }: AdvancedOptionsSectionProps) => {
  const [internalLinksChecked, setInternalLinksChecked] = useState(false);
  
  const handleInternalLinksToggle = (checked: boolean) => {
    if (checked) {
      setInternalLinksChecked(true);
      toast({
        title: "Feature Not Available",
        description: "Not enough generated blogs at the moment",
        variant: "destructive"
      });
      // Turn off the switch after a brief delay (reduced to 0.5 seconds)
      setTimeout(() => {
        setInternalLinksChecked(false);
      }, 500);
    }
  };

  return (
    <Card className="group border-0 bg-white/60 backdrop-blur-sm hover:bg-white/80 transition-all duration-500 hover:shadow-xl hover:shadow-violet-500/10 animate-fade-in w-full" style={{
      animationDelay: '0.3s'
    }}>
      <CardHeader className="pb-2 md:pb-3 px-3 sm:px-4 md:px-6">
        <div className="flex items-center space-x-2 md:space-x-3">
          <div className="p-1.5 md:p-2 bg-gradient-to-br from-violet-500 to-pink-600 rounded-lg text-white group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
            <Settings className="h-3 w-3 md:h-4 md:w-4 lg:h-5 lg:w-5" />
          </div>
          <div className="min-w-0 flex-1">
            <CardTitle className="text-base md:text-lg lg:text-xl truncate">Advanced Options</CardTitle>
            <CardDescription className="text-xs md:text-sm">Fine-tune your blog's features and enhancements</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-3 sm:px-4 md:px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4">
          <div className="flex items-center justify-between p-2.5 md:p-3 lg:p-4 rounded-lg bg-gradient-to-r from-blue-50/50 to-purple-50/50 backdrop-blur-sm border border-gray-200/30 min-w-0">
            <div className="space-y-0.5 md:space-y-1 flex-1 pr-2 md:pr-3 min-w-0">
              <Label className="text-xs md:text-sm font-medium">Include Diagrams</Label>
              <p className="text-xs text-gray-600">Add diagrams and illustrations within the content</p>
            </div>
            <Switch 
              checked={formData.include_image === "yes"} 
              onCheckedChange={checked => onFormDataChange({
                ...formData,
                include_image: checked ? "yes" : "no"
              })} 
              className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-blue-500 data-[state=checked]:to-purple-600 flex-shrink-0" 
            />
          </div>
          <div className="flex items-center justify-between p-2.5 md:p-3 lg:p-4 rounded-lg bg-gradient-to-r from-emerald-50/50 to-teal-50/50 backdrop-blur-sm border border-gray-200/30 min-w-0">
            <div className="space-y-0.5 md:space-y-1 flex-1 pr-2 md:pr-3 min-w-0">
              <Label className="text-xs md:text-sm font-medium">Internal Links</Label>
              <p className="text-xs text-gray-600">Adds relevant internally generated blog links</p>
            </div>
            <Switch 
              checked={internalLinksChecked} 
              onCheckedChange={handleInternalLinksToggle}
              className="flex-shrink-0 data-[state=unchecked]:bg-emerald-200/60 data-[state=checked]:bg-emerald-500" 
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
