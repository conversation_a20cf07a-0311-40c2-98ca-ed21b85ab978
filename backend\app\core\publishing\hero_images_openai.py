"""
OpenAI GPT-Image-1 Hero Image Generator

This module provides hero image generation using OpenAI's GPT-Image-1 model.
It supports both text-to-image generation and image-to-image editing.
"""

import os
import base64
import time
from io import BytesIO
from typing import List, Optional, Union
from PIL import Image
import requests
from openai import OpenAI
from dotenv import load_dotenv

# Import existing utilities
from app.utils.config import llm_config, llm_config_claude, openai_image_config
from app.utils.helpers import sanitize_text, upload_file_to_s3, get_random_image_path
from app.core.publishing.hero_images import (
    generate_prompt,
    text_for_image,
    add_text_to_image,
)

load_dotenv()


class OpenAIImageGenerator:
    """OpenAI GPT-Image-1 image generator class."""

    def __init__(self):
        """Initialize OpenAI client with proper configuration."""
        self.api_key = openai_image_config["api_key"]
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")

        self.client = OpenAI(api_key=self.api_key)

        # Configuration options from environment
        self.default_size = openai_image_config["default_size"]
        self.default_quality = openai_image_config["default_quality"]
        self.default_output_format = openai_image_config["default_format"]
        self.max_retries = 3
        self.retry_delay = 2  # seconds

    def text_to_image(
        self,
        prompt: str,
        size: str = None,
        quality: str = None,
        background: str = "auto",
        output_format: str = None,
        n: int = 1,
    ) -> bytes:
        """
        Generate image from text prompt using OpenAI GPT-Image-1.

        Args:
            prompt (str): Text description for image generation
            size (str): Image size - "1024x1024", "1536x1024", or "1024x1536"
            quality (str): Image quality - "low", "medium", or "high"
            background (str): Background type - "auto", "transparent", or "opaque"
            output_format (str): Output format - "png", "jpeg", or "webp"
            n (int): Number of images to generate (1-10)

        Returns:
            bytes: Generated image as bytes

        Raises:
            Exception: If image generation fails
        """
        # Use defaults if not specified
        size = size or self.default_size
        quality = quality or self.default_quality
        output_format = output_format or self.default_output_format

        print(f"🎨 Generating image with OpenAI GPT-Image-1...")
        print(f"📝 Prompt: {prompt[:100]}...")
        print(f"📐 Size: {size}, Quality: {quality}, Format: {output_format}")

        for attempt in range(self.max_retries):
            try:
                response = self.client.images.generate(
                    model="gpt-image-1",
                    prompt=prompt,
                    n=n,
                    size=size,
                    quality=quality,
                    background=background,
                    output_format=output_format,
                    response_format="b64_json",
                )

                # Decode base64 image
                image_b64 = response.data[0].b64_json
                image_bytes = base64.b64decode(image_b64)

                print(f"✅ Image generated successfully ({len(image_bytes)} bytes)")
                return image_bytes

            except Exception as e:
                print(f"❌ Attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.max_retries - 1:
                    print(f"⏳ Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    raise Exception(
                        f"Failed to generate image after {self.max_retries} attempts: {str(e)}"
                    )

    def image_to_image(
        self,
        prompt: str,
        image_paths: Union[str, List[str]],
        size: str = None,
        mask_path: Optional[str] = None,
        n: int = 1,
    ) -> bytes:
        """
        Edit/modify images using OpenAI GPT-Image-1 with image input.

        Args:
            prompt (str): Text description for image editing
            image_paths (Union[str, List[str]]): Path(s) to input image(s)
            size (str): Output image size
            mask_path (Optional[str]): Path to mask image for inpainting
            n (int): Number of images to generate

        Returns:
            bytes: Edited image as bytes

        Raises:
            Exception: If image editing fails
        """
        # Ensure image_paths is a list
        if isinstance(image_paths, str):
            image_paths = [image_paths]

        size = size or self.default_size

        print(f"🖼️ Editing image(s) with OpenAI GPT-Image-1...")
        print(f"📝 Prompt: {prompt[:100]}...")
        print(f"🖼️ Input images: {len(image_paths)}")
        print(f"📐 Size: {size}")

        for attempt in range(self.max_retries):
            try:
                # Prepare image files
                image_files = []
                for image_path in image_paths:
                    if os.path.exists(image_path):
                        image_files.append(open(image_path, "rb"))
                    else:
                        # Try to download if it's a URL
                        if image_path.startswith(("http://", "https://")):
                            response = requests.get(image_path)
                            image_files.append(BytesIO(response.content))
                        else:
                            raise FileNotFoundError(
                                f"Image file not found: {image_path}"
                            )

                # Prepare edit request parameters
                edit_params = {
                    "model": "gpt-image-1",
                    "image": image_files,
                    "prompt": prompt,
                    "n": n,
                    "size": size,
                    "response_format": "b64_json",
                }

                # Add mask if provided
                if mask_path and os.path.exists(mask_path):
                    edit_params["mask"] = open(mask_path, "rb")

                response = self.client.images.edit(**edit_params)

                # Clean up file handles
                for file_obj in image_files:
                    if hasattr(file_obj, "close"):
                        file_obj.close()

                if mask_path and os.path.exists(mask_path):
                    edit_params["mask"].close()

                # Decode base64 image
                image_b64 = response.data[0].b64_json
                image_bytes = base64.b64decode(image_b64)

                print(f"✅ Image edited successfully ({len(image_bytes)} bytes)")
                return image_bytes

            except Exception as e:
                print(f"❌ Attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.max_retries - 1:
                    print(f"⏳ Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    raise Exception(
                        f"Failed to edit image after {self.max_retries} attempts: {str(e)}"
                    )


def get_openai_image_generator() -> OpenAIImageGenerator:
    """
    Get configured OpenAI image generator instance.

    Returns:
        OpenAIImageGenerator: Configured generator instance
    """
    return OpenAIImageGenerator()


def openai_text_to_image(prompt: str, **kwargs) -> bytes:
    """
    Generate image from text using OpenAI GPT-Image-1.

    Args:
        prompt (str): Text prompt for image generation
        **kwargs: Additional parameters for image generation

    Returns:
        bytes: Generated image as bytes
    """
    generator = get_openai_image_generator()
    return generator.text_to_image(prompt, **kwargs)


def openai_image_to_image(
    prompt: str, image_paths: Union[str, List[str]], **kwargs
) -> bytes:
    """
    Edit image using OpenAI GPT-Image-1.

    Args:
        prompt (str): Text prompt for image editing
        image_paths (Union[str, List[str]]): Input image path(s)
        **kwargs: Additional parameters for image editing

    Returns:
        bytes: Edited image as bytes
    """
    generator = get_openai_image_generator()
    return generator.image_to_image(prompt, image_paths, **kwargs)


def generate_hero_image_openai(
    text: str, use_reference_image: bool = True, generation_mode: str = "auto"
) -> str:
    """
    Generate hero image using OpenAI GPT-Image-1.

    This function maintains compatibility with the existing hero image generation API
    while using OpenAI's GPT-Image-1 model instead of Leonardo AI.

    Args:
        text (str): Table of contents or content description
        use_reference_image (bool): Whether to use reference image for editing
        generation_mode (str): "text_to_image", "image_to_image", or "auto"

    Returns:
        str: URL of the generated hero image uploaded to S3

    Raises:
        Exception: If image generation or upload fails
    """
    try:
        # Process input text
        if isinstance(text, list):
            content_text = ",".join(text)
        else:
            content_text = text

        # Generate prompt using existing logic
        prompt = generate_prompt(content_text)
        print(f"🎯 Generated prompt: {prompt}")

        # Determine generation mode
        if generation_mode == "auto":
            generation_mode = (
                "image_to_image" if use_reference_image else "text_to_image"
            )

        # Generate image based on mode
        if generation_mode == "image_to_image" and use_reference_image:
            # Use reference image for editing
            reference_image_path = get_random_image_path()
            print(f"🖼️ Using reference image: {reference_image_path}")
            image_bytes = openai_image_to_image(
                prompt, reference_image_path, size="1024x544"
            )
        else:
            # Pure text-to-image generation
            print("🎨 Using text-to-image generation")
            image_bytes = openai_text_to_image(prompt, size="1024x544", quality="high")

        # Convert bytes to PIL Image for text overlay
        image = Image.open(BytesIO(image_bytes))

        # Add text overlay using existing function
        image_url = add_text_to_image(content_text, image)

        if not image_url:
            raise Exception("Failed to upload image to S3")

        print(f"✅ Hero image generated and uploaded: {image_url}")
        return image_url

    except Exception as e:
        print(f"❌ Error in OpenAI hero image generation: {str(e)}")
        raise e
