from __future__ import annotations

"""
OpenAI GPT‑Image‑1 Hero Image Generator (rev.‑2025‑06‑29)
-------------------------------------------------------
Cleaned‑up, production‑ready module that supports both text‑to‑image *and*
image‑to‑image workflows with sensible fall‑backs and clear error handling.

Major fixes compared with the previous version
==============================================
*   **Verification guard** – catches the 403 "organisation must be verified" error
    early and explains how to resolve it, instead of silently retrying.
*   **Supported sizes only** – maps any 16:9 or 9:16 request to a legal size
    *before* the API call and uses Pillow for post‑resize when required.
*   **Context‑managed file handling** – prevents leaked file descriptors on
    error paths.
*   **Edit endpoint clean‑up** – removes the unsupported `response_format`
    parameter and only sends the single primary image (the *edits* endpoint
    currently ignores extra images).
*   **Graceful fall‑back** – optional automatic downgrade to `dall‑e‑3` for
    *generate* calls if `gpt‑image‑1` is blocked.
*   **Version guard** – refuses to run on `openai<1.78.0`, because earlier
    releases lack up‑to‑date image parameters.

The public helpers `openai_text_to_image`, `openai_image_to_image`, and
`generate_hero_image_openai` keep their original signatures so the rest of the
application does *not* need to change.
"""

from io import BytesIO
import os
import sys
import time
import base64
from typing import List, Optional, Union, Tuple, Iterable

import requests
from PIL import Image
from dotenv import load_dotenv

try:
    import openai  # type: ignore
except ImportError:  # pragma: no cover – developer mis‑install
    raise RuntimeError("The `openai` package is required – run `pip install openai`. ")

# ──────────────────────────────────────────────────────────────────────────────
# Basic guards & env‑load
# ──────────────────────────────────────────────────────────────────────────────

MIN_OPENAI_VERSION: Tuple[int, int, int] = (1, 78, 0)


def _version_tuple(ver: str) -> Tuple[int, int, int]:
    return tuple(map(int, ver.split(".")[:3]))  # type: ignore


if _version_tuple(openai.__version__) < MIN_OPENAI_VERSION:
    raise RuntimeError(
        f"`openai` {openai.__version__} is too old. Upgrade to ≥{'.'.join(map(str, MIN_OPENAI_VERSION))}."
    )

load_dotenv()

# Import existing utilities from your codebase (unchanged)
from app.utils.config import openai_image_config  # noqa: E402
from app.utils.helpers import (
    sanitize_text,
    upload_file_to_s3,
    get_random_image_path,
)  # noqa: E402
from app.core.publishing.hero_images import (
    generate_prompt,
    text_for_image,
    add_text_to_image,
)  # noqa: E402

# ──────────────────────────────────────────────────────────────────────────────
# Core generator class
# ──────────────────────────────────────────────────────────────────────────────


class OpenAIImageGenerator:
    """Wrapper around the OpenAI Python SDK with safe defaults."""

    # Model constants – tweak if OpenAI changes naming
    MODEL_GPT_IMAGE = "gpt-image-1"
    MODEL_DALLE3 = "dall-e-3"  # back‑up for *generate* only

    SUPPORTED_SIZES = {
        "1024x1024": (1024, 1024),
        "1536x1024": (1536, 1024),  # landscape 3∶2 ~ 16∶10
        "1024x1536": (1024, 1536),  # portrait
    }

    _SIZE_FALLBACKS = {
        # 16∶9 ↦ landscape;  9∶16 ↦ portrait
        "1024x544": "1536x1024",
        "1280x720": "1536x1024",
        "1920x1080": "1536x1024",
        "960x540": "1536x1024",
        "544x1024": "1024x1536",
        "720x1280": "1024x1536",
        "1080x1920": "1024x1536",
    }

    # Retry configuration
    MAX_RETRIES = 3
    RETRY_DELAY = 2  # seconds

    def __init__(self) -> None:
        api_key = openai_image_config.get("api_key") or os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise RuntimeError("OPENAI_API_KEY environment variable is required.")

        self._client = openai.OpenAI(api_key=api_key)
        self._default_size = openai_image_config.get("default_size", "1536x1024")
        self._default_quality = openai_image_config.get("default_quality", "high")
        self._default_output_format = openai_image_config.get("default_format", "png")

    # ──────────────────────────────────────────────────────────────────────
    # Helpers
    # ──────────────────────────────────────────────────────────────────────

    def _map_size(self, requested: str | None) -> str:
        """Return a gpt‑image‑1 compatible size string."""
        if not requested:
            return self._default_size

        if requested in self.SUPPORTED_SIZES:
            return requested
        if requested in self._SIZE_FALLBACKS:
            mapped = self._SIZE_FALLBACKS[requested]
            print(f"⚠️ size {requested} not supported → using {mapped} instead")
            return mapped

        print("⚠️ unknown size %s → defaulting to %s" % (requested, self._default_size))
        return self._default_size

    def _resize_if_needed(self, raw: bytes, original: str, generated: str) -> bytes:
        """Resize *raw* (PNG bytes) to *original* dimensions if they differ."""
        if original == generated or original in self.SUPPORTED_SIZES:
            return raw
        try:
            tgt_w, tgt_h = map(int, original.split("x"))
            with Image.open(BytesIO(raw)) as im:
                im = im.resize((tgt_w, tgt_h), Image.Resampling.LANCZOS)
                buf = BytesIO()
                im.save(buf, format="PNG")
                print(f"🔄 resized from {generated} → {original}")
                return buf.getvalue()
        except Exception as exc:  # pragma: no cover – Pillow edge cases
            print(f"⚠️ resize failed: {exc}; returning original bytes")
            return raw

    # ──────────────────────────────────────────────────────────────────────
    # Public API – text → image
    # ──────────────────────────────────────────────────────────────────────

    def text_to_image(
        self,
        prompt: str,
        *,
        size: str | None = None,
        quality: str | None = None,
        background: str = "auto",
        output_format: str | None = None,
        n: int = 1,
    ) -> bytes:
        size_mapped = self._map_size(size)
        quality = quality or self._default_quality
        output_format = output_format or self._default_output_format

        params = dict(
            model=self.MODEL_GPT_IMAGE,
            prompt=prompt,
            n=n,
            size=size_mapped,
            quality=quality,
            background=background,
            output_format=output_format,
            response_format="b64_json",
        )

        for attempt in range(self.MAX_RETRIES):
            try:
                rsp = self._client.images.generate(**params)
                raw = base64.b64decode(rsp.data[0].b64_json)
                return self._resize_if_needed(raw, size or size_mapped, size_mapped)
            except openai.OpenAIError as err:
                # Special‑case: organisation not verified
                if getattr(
                    err, "http_status", None
                ) == 403 and "must be verified" in str(err):
                    raise RuntimeError(
                        "Your organisation is not verified for gpt‑image‑1. Go to the OpenAI dashboard → Settings → Organisation → Verify. "  # noqa: E501
                    ) from err

                # Fallback to DALL‑E‑3 for *generate* only (no edits)
                if (
                    attempt == 0
                    and self.MODEL_DALLE3
                    and "must be verified" in str(err)
                ):
                    print("🔁 falling back to dall‑e‑3")
                    params["model"] = self.MODEL_DALLE3
                    params.pop("background", None)  # not supported on DALL‑E‑3
                    continue

                if attempt >= self.MAX_RETRIES - 1:
                    raise
                print(f"❌ attempt {attempt+1} failed: {err}; retrying…")
                time.sleep(self.RETRY_DELAY)

    # ──────────────────────────────────────────────────────────────────────
    # Public API – image → image (edit)
    # ──────────────────────────────────────────────────────────────────────

    def image_to_image(
        self,
        prompt: str,
        image_paths: Union[str, List[str]],
        *,
        size: str | None = None,
        mask_path: Optional[str] = None,
        n: int = 1,
    ) -> bytes:
        size_mapped = self._map_size(size)

        # Normalise to list for convenience
        if isinstance(image_paths, str):
            image_paths = [image_paths]
        if not image_paths:
            raise ValueError("At least one input image is required for edits.")

        def _open_images(paths: Iterable[str]):
            for p in paths:
                if os.path.exists(p):
                    yield open(p, "rb")
                elif p.startswith(("http://", "https://")):
                    yield BytesIO(requests.get(p, timeout=10).content)
                else:
                    raise FileNotFoundError(p)

        with contextlib.ExitStack() as stack:
            files = [stack.enter_context(f) for f in _open_images(image_paths)]
            edit_params = dict(
                model=self.MODEL_GPT_IMAGE,
                image=files[0],
                prompt=prompt,
                n=n,
                size=size_mapped,
            )
            if mask_path and os.path.exists(mask_path):
                edit_params["mask"] = stack.enter_context(open(mask_path, "rb"))

            for attempt in range(self.MAX_RETRIES):
                try:
                    rsp = self._client.images.edit(**edit_params)
                    item = rsp.data[0]
                    if getattr(item, "b64_json", None):
                        raw = base64.b64decode(item.b64_json)
                    else:
                        raw = requests.get(item.url, timeout=10).content
                    return self._resize_if_needed(raw, size or size_mapped, size_mapped)
                except openai.OpenAIError as err:
                    if attempt >= self.MAX_RETRIES - 1:
                        raise
                    print(f"❌ attempt {attempt+1} failed: {err}; retrying…")
                    time.sleep(self.RETRY_DELAY)


# ──────────────────────────────────────────────────────────────────────────────
# User‑facing helper wrappers (original API)
# ──────────────────────────────────────────────────────────────────────────────

generator_singleton: Optional[OpenAIImageGenerator] = None


def _get_generator() -> OpenAIImageGenerator:
    global generator_singleton
    if generator_singleton is None:
        generator_singleton = OpenAIImageGenerator()
    return generator_singleton


def openai_text_to_image(prompt: str, **kwargs) -> bytes:
    return _get_generator().text_to_image(prompt, **kwargs)


def openai_image_to_image(
    prompt: str, image_paths: Union[str, List[str]], **kwargs
) -> bytes:
    return _get_generator().image_to_image(prompt, image_paths, **kwargs)


# ──────────────────────────────────────────────────────────────────────────────
# High‑level hero‑image orchestration
# ──────────────────────────────────────────────────────────────────────────────


def generate_hero_image_openai(
    text: str,
    *,
    use_reference_image: bool = True,
    generation_mode: str = "auto",
) -> str:
    """Generate + upload a hero image and return its public URL."""

    content_text = ",".join(text) if isinstance(text, list) else text
    prompt = generate_prompt(content_text)
    print(f"🎯 prompt → {prompt}")

    # Decide workflow
    if generation_mode == "auto":
        generation_mode = "image_to_image" if use_reference_image else "text_to_image"

    if generation_mode == "image_to_image" and use_reference_image:
        reference_path = get_random_image_path()
        print(f"🖼 using reference → {reference_path}")
        raw_bytes = openai_image_to_image(prompt, reference_path, size="1024x544")
    else:
        print("🎨 pure text‑to‑image mode")
        raw_bytes = openai_text_to_image(prompt, size="1024x544", quality="high")

    with Image.open(BytesIO(raw_bytes)) as img:
        url = add_text_to_image(content_text, img)
    if not url:
        raise RuntimeError("failed to upload hero image to S3")
    print(f"✅ hero image uploaded → {url}")
    return url
