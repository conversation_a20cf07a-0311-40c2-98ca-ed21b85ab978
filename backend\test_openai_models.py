#!/usr/bin/env python3
"""
Test script to check available OpenAI models and test basic image generation.
"""

import os
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()

def test_openai_models():
    """Test OpenAI models and basic image generation."""
    print("🔍 Testing OpenAI Models and Image Generation")
    print("="*50)
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY not found")
        return
    
    client = OpenAI(api_key=api_key)
    
    try:
        # Test basic image generation with DALL-E
        print("🎨 Testing basic image generation with DALL-E...")
        
        response = client.images.generate(
            model="dall-e-3",  # Use standard DALL-E model
            prompt="A modern university campus with students using technology",
            n=1,
            size="1024x1024",
            quality="standard"
        )
        
        print(f"✅ Success! Generated image URL: {response.data[0].url}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    test_openai_models()
