
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Sparkles } from "lucide-react";
import { BlogInputData } from "@/pages/Index";

interface BlogSubmitButtonProps {
  formData: BlogInputData;
  isGenerating: boolean;
}

export const BlogSubmitButton = ({ formData, isGenerating }: BlogSubmitButtonProps) => {
  return (
    <div className="flex justify-center pt-2 md:pt-3 lg:pt-4 animate-fade-in w-full" style={{
      animationDelay: '0.4s'
    }}>
      <Button 
        type="submit" 
        disabled={!formData.topic || !formData.receiver_email || isGenerating} 
        size="lg" 
        className="group relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 md:px-8 lg:px-12 py-2.5 md:py-3 text-sm md:text-base lg:text-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none w-full sm:w-auto max-w-sm"
      >
        <span className="relative z-10 flex items-center justify-center space-x-2 md:space-x-3">
          {isGenerating ? <>
            <Loader2 className="h-4 w-4 md:h-5 md:w-5 animate-spin" />
            <span>Generating Your Blog...</span>
          </> : <>
            <Sparkles className="h-4 w-4 md:h-5 md:w-5 group-hover:animate-pulse" />
            <span>Generate Blog Post</span>
          </>}
        </span>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </Button>
    </div>
  );
};
