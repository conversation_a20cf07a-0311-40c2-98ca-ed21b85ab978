# Application settings
APP_NAME=IILM_Blog_Automation
APP_ENV=production
APP_DEBUG=False

# API Authentication
IILM_API_KEY="your_secure_api_key_here"
# Optional: Multiple API keys (comma-separated)
# IILM_API_KEYS="key1,key2,key3"

# OpenAI API Key
OPENAI_API_KEY="your_openai_api_key"
BASE_URL="your_base_url"

# OpenAI Image Generation Configuration
# Note: GPT-Image-1 requires organization verification (ID & face verification)
# Set to true to use OpenAI GPT-Image-1 for hero images, false to use Leonardo AI
USE_OPENAI_IMAGES=false
OPENAI_IMAGE_DEFAULT_SIZE="1024x1024"  # Options: "1024x1024", "1536x1024", "1024x1536"
OPENAI_IMAGE_DEFAULT_QUALITY="high"    # Options: "low", "medium", "high"
OPENAI_IMAGE_DEFAULT_FORMAT="png"      # Options: "png", "jpeg", "webp"

# Claude API Key
CLAUDE_API_KEY="your_claude_api_key"

# MongoDB configuration
MONGO_URI="your_mongo_uri"
MONGO_DB_NAME="SEOLinks"
MONGO_COLLECTION_1="BacklinkDb"
MONGO_COLLECTION_2="ServicePages"

# Google Cloud Storage credentials
GCS_CRED="your_base64_encoded_gcs_credentials"
BUCKET_NAME="your_gcs_bucket_name"

# Webflow configuration
WEBFLOW_API_KEY="your_webflow_api_key"
WEBFLOW_COLLECTION_ID="your_webflow_collection_id"
WEBFLOW_TAGS_COLLECTION_ID="your_webflow_tags_collection_id"
WEBFLOW_CATEGORY_COLLECTION_ID="your_webflow_category_collection_id"
WEBFLOW_SITE_ID="your_webflow_site_id"

# Google ads service keys
GOOGLE_ADS_DEVELOPER_TOKEN="your_google_ads_developer_token"
GOOGLE_ADS_CLIENT_ID="your_google_ads_client_id"
GOOGLE_ADS_CLIENT_SECRET="your_google_ads_client_secret"
GOOGLE_ADS_REFRESH_TOKEN="your_google_ads_refresh_token"
GOOGLE_ADS_LOGIN_CUSTOMER_ID="your_google_ads_login_customer_id"
GOOGLE_ADS_USE_PROTO_PLUS=True

# Other service keys
LEONARDO_API_KEY="your_leonardo_api_key"
ERASER_API_KEY="your_eraser_api_key"
TAVILY_API_KEY="your_tavily_api_key"

# Credentials for sending emails
sender_email="your_sender_email"
sender_password="your_sender_password"
receiver_email="your_receiver_email"
subject="IILM University: New Educational Blog Content Created"
smtp_server="smtp.gmail.com"
email_port=587
sender_name="IILM University Content Team"

# Message Queue Configuration
# Option 1: RabbitMQ (default for local development)
USE_PUBSUB=false
host_ip="your_rabbitmq_host"
rabbitmq_port=5672
rabbit_username="your_rabbitmq_username"
rabbit_password="your_rabbitmq_password"

# Option 2: Google Cloud Pub/Sub (recommended for Cloud Run)
# USE_PUBSUB=true
# GCP_PROJECT_ID="your_gcp_project_id"

# WordPress configuration
WORDPRESS_API_URL="https://your-wordpress-site.com/wp-json/wp/v2"
WORDPRESS_USERNAME="your_wordpress_username"
WORDPRESS_APP_PASSWORD="your_wordpress_app_password"
