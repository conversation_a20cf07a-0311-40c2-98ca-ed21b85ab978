"""
Integration tests for OpenAI hero image generation API endpoint.
"""

import unittest
import json
import os
from unittest.mock import patch, <PERSON><PERSON>
import sys

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app import create_app


class TestHeroImageOpenAIIntegration(unittest.TestCase):
    """Integration tests for OpenAI hero image generation."""

    def setUp(self):
        """Set up test environment."""
        # Set up environment variables for testing
        self.env_patcher = patch.dict(
            os.environ,
            {
                "OPENAI_API_KEY": "test_openai_key",
                "USE_OPENAI_IMAGES": "true",
                "IILM_API_KEY": "test_api_key",
                "AWS_ACCESS_KEY": "test_aws_key",
                "AWS_SECRET_KEY": "test_aws_secret",
                "AWS_BUCKET_NAME": "test_bucket",
                "AWS_REGION": "us-east-1",
            },
        )
        self.env_patcher.start()

        # Create Flask test client
        self.app = create_app()
        self.app.config["TESTING"] = True
        self.client = self.app.test_client()

        # Test headers with API key
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_api_key",
        }

    def tearDown(self):
        """Clean up test environment."""
        self.env_patcher.stop()

    @patch("app.core.publishing.hero_images_openai.generate_hero_image_openai")
    def test_hero_image_generation_openai_success(self, mock_generate_openai):
        """Test successful hero image generation using OpenAI."""
        # Mock successful OpenAI generation
        mock_generate_openai.return_value = "https://s3.amazonaws.com/test-hero-image.jpg"

        payload = {
            "table_of_contents": "Introduction to AI\nMachine Learning Basics\nConclusion"
        }

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn("hero_image_url", data)
        self.assertIn("provider_used", data)
        self.assertEqual(data["hero_image_url"], "https://s3.amazonaws.com/test-hero-image.jpg")
        self.assertEqual(data["provider_used"], "openai")

        mock_generate_openai.assert_called_once_with(
            "Introduction to AI\nMachine Learning Basics\nConclusion"
        )

    @patch("app.core.publishing.hero_images.hero_image_generation")
    @patch("app.core.publishing.hero_images_openai.generate_hero_image_openai")
    def test_hero_image_generation_openai_fallback_to_leonardo(
        self, mock_generate_openai, mock_generate_leonardo
    ):
        """Test fallback to Leonardo when OpenAI fails."""
        # Mock OpenAI failure and Leonardo success
        mock_generate_openai.side_effect = Exception("OpenAI API Error")
        mock_generate_leonardo.return_value = "https://s3.amazonaws.com/leonardo-hero-image.jpg"

        payload = {
            "table_of_contents": "Introduction to AI\nMachine Learning Basics\nConclusion"
        }

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data["hero_image_url"], "https://s3.amazonaws.com/leonardo-hero-image.jpg")
        self.assertEqual(data["provider_used"], "leonardo")

        mock_generate_openai.assert_called_once()
        mock_generate_leonardo.assert_called_once()

    @patch("app.core.publishing.hero_images.hero_image_generation")
    def test_hero_image_generation_force_leonardo(self, mock_generate_leonardo):
        """Test forcing Leonardo provider via request parameter."""
        mock_generate_leonardo.return_value = "https://s3.amazonaws.com/leonardo-hero-image.jpg"

        payload = {
            "table_of_contents": "Introduction to AI\nMachine Learning Basics\nConclusion",
            "force_provider": "leonardo"
        }

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data["provider_used"], "leonardo")
        mock_generate_leonardo.assert_called_once()

    @patch("app.core.publishing.hero_images_openai.generate_hero_image_openai")
    def test_hero_image_generation_force_openai(self, mock_generate_openai):
        """Test forcing OpenAI provider via request parameter."""
        mock_generate_openai.return_value = "https://s3.amazonaws.com/openai-hero-image.jpg"

        payload = {
            "table_of_contents": "Introduction to AI\nMachine Learning Basics\nConclusion",
            "force_provider": "openai"
        }

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data["provider_used"], "openai")
        mock_generate_openai.assert_called_once()

    def test_hero_image_generation_missing_toc(self):
        """Test hero image generation with missing table of contents."""
        payload = {}

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn("error", data)
        self.assertIn("Table of contents cannot be empty", data["error"])

    def test_hero_image_generation_empty_toc(self):
        """Test hero image generation with empty table of contents."""
        payload = {"table_of_contents": ""}

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn("error", data)
        self.assertIn("Table of contents cannot be empty", data["error"])

    def test_hero_image_generation_invalid_json(self):
        """Test hero image generation with invalid JSON."""
        response = self.client.post(
            "/api/content/hero-image",
            data="invalid json",
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn("error", data)
        self.assertIn("Request must be JSON", data["error"])

    def test_hero_image_generation_no_auth(self):
        """Test hero image generation without authentication."""
        payload = {
            "table_of_contents": "Introduction to AI\nMachine Learning Basics\nConclusion"
        }

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers={"Content-Type": "application/json"},  # No auth header
        )

        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertIn("error", data)
        self.assertIn("Unauthorized", data["error"])

    @patch("app.core.publishing.hero_images_openai.generate_hero_image_openai")
    @patch("app.core.publishing.hero_images.hero_image_generation")
    def test_hero_image_generation_both_providers_fail(
        self, mock_generate_leonardo, mock_generate_openai
    ):
        """Test when both OpenAI and Leonardo providers fail."""
        # Both providers fail
        mock_generate_openai.side_effect = Exception("OpenAI API Error")
        mock_generate_leonardo.side_effect = Exception("Leonardo API Error")

        payload = {
            "table_of_contents": "Introduction to AI\nMachine Learning Basics\nConclusion"
        }

        response = self.client.post(
            "/api/content/hero-image",
            data=json.dumps(payload),
            headers=self.headers,
        )

        self.assertEqual(response.status_code, 500)
        data = json.loads(response.data)
        self.assertIn("error", data)


class TestHeroImageConfigurationIntegration(unittest.TestCase):
    """Integration tests for hero image configuration."""

    def setUp(self):
        """Set up test environment."""
        self.app = create_app()
        self.app.config["TESTING"] = True

    @patch.dict(os.environ, {"USE_OPENAI_IMAGES": "false"}, clear=False)
    @patch("app.core.publishing.hero_images.hero_image_generation")
    def test_leonardo_default_when_openai_disabled(self, mock_generate_leonardo):
        """Test that Leonardo is used when OpenAI is disabled."""
        mock_generate_leonardo.return_value = "https://s3.amazonaws.com/leonardo-image.jpg"

        with self.app.test_client() as client:
            payload = {
                "table_of_contents": "Test content"
            }
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer test_api_key",
            }

            with patch.dict(os.environ, {"IILM_API_KEY": "test_api_key"}):
                response = client.post(
                    "/api/content/hero-image",
                    data=json.dumps(payload),
                    headers=headers,
                )

            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data["provider_used"], "leonardo")
            mock_generate_leonardo.assert_called_once()

    @patch.dict(os.environ, {"USE_OPENAI_IMAGES": "true", "OPENAI_API_KEY": "test_key"}, clear=False)
    @patch("app.core.publishing.hero_images_openai.generate_hero_image_openai")
    def test_openai_default_when_enabled(self, mock_generate_openai):
        """Test that OpenAI is used when enabled."""
        mock_generate_openai.return_value = "https://s3.amazonaws.com/openai-image.jpg"

        with self.app.test_client() as client:
            payload = {
                "table_of_contents": "Test content"
            }
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer test_api_key",
            }

            with patch.dict(os.environ, {"IILM_API_KEY": "test_api_key"}):
                response = client.post(
                    "/api/content/hero-image",
                    data=json.dumps(payload),
                    headers=headers,
                )

            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data["provider_used"], "openai")
            mock_generate_openai.assert_called_once()


if __name__ == "__main__":
    unittest.main()
