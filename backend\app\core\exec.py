import os
import json
import concurrent.futures
from functools import partial
from typing import List, Tuple, Dict, Any, Optional
from app.core.content.toc import (
    toc_generator,
    toc_generator_technical,
    toc_generator_short,
    table_of_content_formatter,
)
from app.core.content.decider import table_of_content_function

# from app.core.content.topic import generate_topic
from app.core.content.writer import (
    content_technical,
    content_informative,
    format_blog_content,
    format_bullet_points,
    toning_agent,
)
from app.core.content.images import in_blog_image, diagram_reviewer
from app.core.content.links import get_internal_link
from app.core.content.enhancer import get_keywords
from app.core.publishing.wordpress import post_to_wordpress, retry_on_529_error
from app.core.publishing.metadata import (
    get_summary,
    meta_description_and_title,
    get_summary,
)
from app.core.publishing.hero_images import hero_image_generation
from app.utils.helpers import (
    upload_file_to_s3,
    download_file_from_s3,
    slug_generator,
)


# Helper functions for concurrent processing
def generate_content(points: List[str], is_technical: bool) -> str:
    """Generate content for a set of points"""
    points_str = "\n".join(points)
    prompt = f"Elaborate the points - \n{points_str}"

    print(
        f"Started execution of subtopic writer for points: {', '.join(points[:2])}..."
    )
    if is_technical:
        content = content_technical(prompt)
    else:
        content = content_informative(prompt)
    print("Finished executing subtopic writer")
    return content


def process_content_batch(
    points: List[str], is_technical: bool, batch_index: int
) -> Tuple[str, int]:
    """Process a batch of points and return the formatted content with its index"""
    # Generate initial content
    content = generate_content(points, is_technical)

    # Format blog content
    print("Started execution of formatting blog content...")
    formatted_content_1 = format_blog_content(content)
    print("Finished executing formatting blog content")

    # Format bullet points
    print("Started execution of formatting bullet points...")
    formatted_content = format_bullet_points(formatted_content_1)
    print("Finished executing formatting bullet points")

    # Apply toning agent
    print("Started execution of Toner Agent...")
    toned_content = toning_agent(formatted_content)
    print("Finished executing Toner Agent")

    # Add keywords
    print("Started execution of keyword adders...")
    keyword_content = get_keywords(toned_content)
    print("Finished executing keyword adders")

    # Add internal links
    print("Started execution of internal link adders...")
    internal_content = get_internal_link(keyword_content)
    print("Finished executing internal link adders")

    return internal_content, batch_index


def process(title):
    """Process a title into a blog post with concurrent execution"""
    json_points, technical = table_of_content_function(title)

    # Prepare batches of points
    batches = []
    i = 0
    while i < len(json_points):
        if i + 2 < len(json_points):
            # Create batches of 3 points
            batches.append((json_points[i : i + 3], i // 3))
            i += 3
        else:
            # Handle remaining points
            batches.append((json_points[i:], i // 3))
            break

    # Process batches concurrently
    results = []
    with concurrent.futures.ThreadPoolExecutor(
        max_workers=min(len(batches), 12)
    ) as executor:
        # Submit all tasks
        future_to_batch = {
            executor.submit(process_content_batch, points, technical, idx): (
                points,
                idx,
            )
            for points, idx in batches
        }

        # Collect results as they complete
        for future in concurrent.futures.as_completed(future_to_batch):
            try:
                content, idx = future.result()
                results.append((content, idx))
                print(
                    f"Completed batch {idx} with {len(future_to_batch[future][0])} points"
                )
            except Exception as e:
                points, idx = future_to_batch[future]
                print(f"Batch {idx} with points {points} generated an exception: {e}")
                # Fall back to sequential processing for failed batch
                try:
                    content = generate_content(points, technical)
                    formatted_content = format_blog_content(content)
                    formatted_content = format_bullet_points(formatted_content)
                    toned_content = toning_agent(formatted_content)
                    keyword_content = get_keywords(toned_content)
                    internal_content = get_internal_link(keyword_content)
                    results.append((internal_content, idx))
                except Exception as e2:
                    print(f"Fallback processing also failed for batch {idx}: {e2}")
                    # Add empty content as placeholder to maintain order
                    results.append(("", idx))

    # Sort results by batch index to maintain original order
    results.sort(key=lambda x: x[1])

    # Combine all content
    string_new = "\n".join(content for content, _ in results)

    return string_new, title, json_points


def automate():
    # Import here to avoid circular import
    from app.core.content.topic import generate_topic

    topic = generate_topic()
    json_points, blog_type = table_of_content_function(topic)
    if blog_type:
        blog_type = "technical"
    else:
        blog_type = "informative"

    # Start background tasks as soon as we have the table of content
    # But don't wait for them to complete - just get the futures
    print("=== STARTING BACKGROUND TASKS ===")
    background_futures = run_background_tasks(topic, json_points)

    # Immediately start content generation without waiting for background tasks
    print("=== STARTING CONTENT GENERATION ===")
    print("Content generation is running in parallel with background tasks")
    content = manual_blog_process(topic, json_points, blog_type, "no", "no")
    automated = "Yes"
    print("=== CONTENT GENERATION COMPLETE ===")

    # Now collect the results from background tasks (will wait only if needed)
    print("=== CHECKING BACKGROUND TASKS STATUS ===")
    background_results = collect_background_results(background_futures)
    print("=== ALL TASKS COMPLETE ===")

    # Use the results from background tasks in post_to_wordpress
    print("=== POSTING TO WORDPRESS ===")
    status, _ = post_to_wordpress(
        content,
        topic,
        json_points,
        background_results.get("slug"),
        background_results.get("image_url"),
        background_results.get("meta_title"),
        background_results.get("meta_description"),
    )

    entry = {topic: status, "automated": automated}

    # Ensure data directory exists
    import os

    if not os.path.exists("data"):
        os.makedirs("data")

    download_file_from_s3("blog_results.json", "blog_results.json")

    with open("data/blog_results.json", "a") as file:
        json.dump(entry, file)
        file.write("\n")
    upload_file_to_s3("blog_results.json")


def process_manual_batch(
    points: List[str],
    blog_type: str,
    topic: str,
    link_list: List[str],
    include_image: str,
    internal_links_decider: str,
    batch_index: int,
    image_count_ref: List[int],
) -> Tuple[str, List[str], int, str]:
    """Process a batch of points for manual blog generation"""
    # Generate initial content
    points_str = "\n".join(points)
    prompt = f"Elaborate the points - \n{points_str}"

    print(f"Started execution of subtopic writer for batch {batch_index}...")
    if blog_type.lower() == "technical":
        content = content_technical(prompt)
    else:
        content = content_informative(prompt)
    print(f"Finished executing subtopic writer for batch {batch_index}")

    # Format blog content
    print(f"Started execution of formatting blog content for batch {batch_index}...")
    formatted_content_1 = format_blog_content(content)
    print(f"Finished executing formatting blog content for batch {batch_index}")

    # Format bullet points
    print(f"Started execution of formatting bullet points for batch {batch_index}...")
    formatted_content = format_bullet_points(formatted_content_1)
    print(f"Finished executing formatting bullet points for batch {batch_index}")

    # Apply toning agent
    print(f"Started execution of Toner Agent for batch {batch_index}...")
    toned_content = toning_agent(formatted_content)
    print(f"Finished executing Toner Agent for batch {batch_index}")

    # Add keywords
    print(f"Started execution of keyword adders for batch {batch_index}...")
    keyword_content = ""
    # keyword_content = get_keywords(toned_content)
    print(f"Finished executing keyword adders for batch {batch_index}")

    # Process internal links if needed (service links removed as per requirements)
    if internal_links_decider and internal_links_decider.lower() == "yes":
        print(
            f"Started execution of internal link processing for batch {batch_index}..."
        )

        # Only process internal links from BlogLinks collection (no service links)
        internal_content, more_links = get_internal_link(
            toned_content, link_list, topic
        )

        new_links = []
        if more_links:
            new_links.extend(more_links)
            print(f"Added {len(more_links)} internal links for batch {batch_index}")
        else:
            print(f"No internal links found for batch {batch_index}")

        print(f"Finished executing internal link processing for batch {batch_index}")
    else:
        internal_content = toned_content
        new_links = []

    # Process images if needed
    diagram_line = ""
    if include_image == "yes" and image_count_ref[0] < 3:
        var = diagram_reviewer(content)
        if var == "True":
            # Use atomic update of the shared counter
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=1
            ) as counter_executor:

                def increment_counter():
                    if image_count_ref[0] < 3:
                        image_count_ref[0] += 1
                        return True
                    return False

                should_generate = counter_executor.submit(increment_counter).result()

                if should_generate:
                    diagram_line = in_blog_image(content)

    return internal_content, new_links, batch_index, diagram_line


def manual_blog_process(
    topic, json_points, blog_type, include_image=None, internal_links_decider=None
):
    """Process a blog with concurrent execution for manual blog generation"""
    # Shared state that needs to be managed across threads
    link_list = []
    image_count_ref = [0]  # Using a list as a mutable reference

    # Prepare batches of points
    batches = []
    i = 0
    while i < len(json_points):
        if i + 2 < len(json_points):
            # Create batches of 3 points
            batches.append((json_points[i : i + 3], i // 3))
            i += 3
        else:
            # Handle remaining points
            batches.append((json_points[i:], i // 3))
            break

    # Process batches concurrently
    results = []
    with concurrent.futures.ThreadPoolExecutor(
        max_workers=min(len(batches), 12)
    ) as executor:
        # Submit all tasks
        future_to_batch = {}
        for points, idx in batches:
            future = executor.submit(
                process_manual_batch,
                points,
                blog_type,
                topic,
                link_list.copy(),  # Pass a copy of the current link list
                include_image,
                internal_links_decider,
                idx,
                image_count_ref,
            )
            future_to_batch[future] = (points, idx)

        # Collect results as they complete
        for future in concurrent.futures.as_completed(future_to_batch):
            try:
                content, new_links, idx, diagram_line = future.result()

                # Update the shared link list with new links (thread-safe operation)
                if new_links:
                    link_list.extend(new_links)

                results.append((content, idx, diagram_line))
                print(
                    f"Completed batch {idx} with {len(future_to_batch[future][0])} points"
                )
            except Exception as e:
                points, idx = future_to_batch[future]
                print(f"Batch {idx} with points {points} generated an exception: {e}")
                # Add empty content as placeholder to maintain order
                results.append(("", idx, ""))

    # Sort results by batch index to maintain original order
    results.sort(key=lambda x: x[1])

    # Combine all content
    string_new = ""
    for content, _, diagram_line in results:
        if diagram_line:
            string_new = string_new + "\n" + content + "\n\n" + diagram_line
        else:
            string_new = string_new + "\n" + content

    return string_new


# Background task functions for parallel processing
def run_slug_generation(title):
    """Generate a slug for the blog post title in a separate thread"""
    print("Started slug generation...")
    slug = retry_on_529_error(slug_generator, title)
    print("Finished slug generation")
    return slug


def run_hero_image_generation(table_of_content):
    """Generate a hero image for the blog post in a separate thread"""
    print("Started hero image generation...")
    image_url = retry_on_529_error(hero_image_generation, table_of_content)
    print("Finished hero image generation")
    return image_url


def run_meta_generation(table_of_content):
    """Generate meta description and title for the blog post in a separate thread"""
    print("Started meta description and title generation...")
    content = retry_on_529_error(meta_description_and_title, table_of_content)
    try:
        if "Title: " in content and "Description: " in content:
            meta_title = content.split("Title: ")[1].split("\n\n")[0]
            meta_description = content.split("Description: ")[1]
        else:
            raise ValueError("Missing 'Title:' or 'Description:' in content")
    except Exception as e:
        print(f"Error parsing meta content: {e}")
        meta_title = ""
        meta_description = ""
    print("Finished meta description and title generation")
    return meta_title, meta_description


def run_background_tasks(title, table_of_content):
    """Run all background tasks in parallel and return the futures"""
    print("Starting all background tasks in parallel...")

    # Create a global executor that will be shared across the application
    # This ensures that the threads are truly running in parallel
    global _background_executor
    if "_background_executor" not in globals():
        _background_executor = concurrent.futures.ThreadPoolExecutor(max_workers=5)

    # Submit all tasks and return futures without waiting
    print("  - Submitting slug generation task")
    future_slug = _background_executor.submit(run_slug_generation, title)

    print("  - Submitting hero image generation task")
    future_image = _background_executor.submit(
        run_hero_image_generation, table_of_content
    )

    print("  - Submitting meta description and title generation task")
    future_meta = _background_executor.submit(run_meta_generation, table_of_content)

    print("All background tasks submitted and running in parallel")
    print("Content generation will now start without waiting for background tasks")

    return {
        "future_slug": future_slug,
        "future_image": future_image,
        "future_meta": future_meta,
    }


def collect_background_results(background_futures):
    """Collect results from background task futures"""
    import concurrent.futures

    results = {}

    # Check which tasks are already done without blocking
    slug_done = background_futures["future_slug"].done()
    image_done = background_futures["future_image"].done()
    meta_done = background_futures["future_meta"].done()

    print(
        f"Background task status before waiting: Slug: {'DONE' if slug_done else 'RUNNING'}, "
        f"Hero Image: {'DONE' if image_done else 'RUNNING'}, "
        f"Meta: {'DONE' if meta_done else 'RUNNING'}"
    )

    # Create a list of futures that are not done yet
    pending_futures = []
    if not slug_done:
        pending_futures.append(background_futures["future_slug"])
    if not image_done:
        pending_futures.append(background_futures["future_image"])
    if not meta_done:
        pending_futures.append(background_futures["future_meta"])

    # If there are pending futures, wait for them to complete
    if pending_futures:
        print(f"Waiting for {len(pending_futures)} background tasks to complete...")
        concurrent.futures.wait(pending_futures)
        print("All background tasks have completed")

    # Now collect the results
    try:
        results["slug"] = background_futures["future_slug"].result()
        print(f"Slug generation completed: {results['slug']}")
    except Exception as e:
        print(f"Slug generation failed: {e}")
        results["slug"] = None

    try:
        results["image_url"] = background_futures["future_image"].result()
        print(f"Hero image generation completed")
    except Exception as e:
        print(f"Hero image generation failed: {e}")
        results["image_url"] = None

    try:
        results["meta_title"], results["meta_description"] = background_futures[
            "future_meta"
        ].result()
        print(f"Meta generation completed")
    except Exception as e:
        print(f"Meta generation failed: {e}")
        results["meta_title"] = ""
        results["meta_description"] = ""

    return results


def blog_generator(
    topic_title,
    table_of_content,
    blog_type,
    blog_length,
    include_image,
    internal_links_decider,
):
    result = {
        "title": topic_title,
        "status": "incomplete",
    }
    try:
        if table_of_content == "":
            if blog_type.lower() == "technical":
                print(
                    f"A {blog_length} technical Table of content is generating for topic {topic_title} "
                )
                json_points = toc_generator_technical(topic_title)
            else:
                if blog_length.lower() == "short":
                    print(
                        f"A {blog_length} informative Table of content is generating for topic {topic_title}"
                    )
                    json_points = toc_generator_short(topic_title)
                else:
                    print(
                        f"A {blog_length} informative Table of content is generating for topic {topic_title}"
                    )
                    json_points = toc_generator(topic_title)

        else:
            print(f"Table of content is given for the {topic_title}")
            json_points = table_of_content_formatter(table_of_content)

        result["toc"] = json_points
        print("The table of content created is of length", len(json_points))

        # Start background tasks as soon as we have the table of content
        # But don't wait for them to complete - just get the futures
        print("=== STARTING BACKGROUND TASKS ===")
        background_futures = run_background_tasks(topic_title, json_points)

        # Immediately start content generation without waiting for background tasks
        print("=== STARTING CONTENT GENERATION ===")
        print("Content generation is running in parallel with background tasks")
        content = manual_blog_process(
            topic_title, json_points, blog_type, include_image, internal_links_decider
        )

        result["content"] = content
        # if os.getenv("APP_ENV") == "dev":
        #     with open("output/blogs/" + topic_title + ".md", "w", encoding="utf-8") as f:
        #         f.write(content)

        print("=== CONTENT GENERATION COMPLETE ===")

        # Now collect the results from background tasks (will wait only if needed)
        print("=== CHECKING BACKGROUND TASKS STATUS ===")
        background_results = collect_background_results(background_futures)
        print("=== ALL TASKS COMPLETE ===")

        result.update(
            {
                "slug": background_results.get("slug"),
                "image_url": background_results.get("image_url"),
                "meta_title": background_results.get("meta_title"),
                "meta_description": background_results.get("meta_description"),
            }
        )

        summary = get_summary(content)
        result["summary"] = summary

        result["status"] = "success"
        print("=== BLOG GENERATION COMPLETE ===")
        return result

    except Exception as e:
        print(f"Error in blog generation: {str(e)}")
        result["error"] = str(e)
        return result
